package com.sencha.tools.test.server;

import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.test.config.JobServerConfig;
import com.sencha.tools.test.config.TestEnvironmentConfig;
import com.sencha.tools.test.server.responder.JobServerResponder;
import com.sencha.util.FileUtil;
import com.sencha.util.SilentHttpServerLogger;
import com.sencha.util.http.Server;
import java.io.File;
import org.slf4j.Logger;

public class JobServer {
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private Server _server;
    private boolean _running = false;
    
    private final int _port;
    private final JobServerContext _context;
    private final JobServerResponder _rootResponder;
    private final TestEnvironmentConfig _config;
    private final String _responderMount;
    private final String _archiveMount;
    
    static {
        JobServerGsonFactory.registerAdaptersWithJsonUtil();
        if(!_logger.isTraceEnabled()) {
            org.eclipse.jetty.util.log.Log.setLog(new SilentHttpServerLogger());
        }
    }
    
    public JobServer(TestEnvironmentConfig config) {
        _config = config;
        JobServerConfig serverCfg = _config.getServerConfig();
        _port = serverCfg.getPort();
        _responderMount = serverCfg.getResponderMount();
        _archiveMount = serverCfg.getArchiveMount();
        _context = new JobServerContext(_config, this);
        _rootResponder = new JobServerResponder(_context);
    }
    
    public synchronized Server getServer() {
        if(_server == null) {
            _server = new Server(_port);
            _server.mount(_responderMount, _rootResponder);
            _server.mount(_archiveMount, _context.getArchivesDirectory());
        }
        return _server;
    }
    
    public synchronized void start() {
        if(!_running) {
            getServer().start();
            _logger.info("Test Server listening on port {}", _port);
            _running = true;
        }
    }
    
    public synchronized void stop() {
        if(_running) {
            _context.stop();
            getServer().stop();
            getServer().waitForStop();
            _server = null;
            _running = false;
            File tmpDir = _context.getArchivesDirectory();
            if(tmpDir.exists()) {
                FileUtil.delete(tmpDir);
            }
        }
    }
}
