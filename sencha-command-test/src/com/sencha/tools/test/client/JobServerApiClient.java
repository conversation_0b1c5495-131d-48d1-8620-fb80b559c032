package com.sencha.tools.test.client;

import com.google.common.reflect.TypeToken;
import com.sencha.exceptions.BasicException;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.test.entities.Browser;
import com.sencha.tools.test.entities.JobAgentMessage;
import com.sencha.tools.test.entities.JobAgent;
import com.sencha.tools.test.entities.JobClient;
import com.sencha.tools.test.entities.Result;
import com.sencha.tools.test.entities.JobServerMessage;
import com.sencha.tools.test.entities.JobClientMessage;
import com.sencha.tools.test.entities.Spec;
import com.sencha.tools.test.entities.Suite;
import com.sencha.tools.test.server.JobServerGsonFactory;
import com.sencha.tools.test.server.responder.UploadResponder.UploadResponse;
import com.sencha.util.SilentHttpServerLogger;
import com.sencha.util.StringUtil;
import java.io.File;
import java.io.FileOutputStream;
import java.net.URL;
import java.util.List;

import static com.sencha.util.CollectionUtil.*;
import com.sencha.util.StreamUtil;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

import com.sencha.util.functional.Func;
import com.sencha.util.http.JsonHttpClient;
import org.slf4j.Logger;

public class JobServerApiClient {
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private final URL _url;
    private final JsonHttpClient _jsonClient;
    
    static {
        JobServerGsonFactory.registerAdaptersWithJsonUtil();
        if(!_logger.isTraceEnabled()) {
            org.eclipse.jetty.util.log.Log.setLog(new SilentHttpServerLogger());
        }
    }
    
    public JobServerApiClient(URL serverUrl) {
        _url = serverUrl;
        _jsonClient = new JsonHttpClient(_url);
    }
    
    private String getRpcPath(String path) {
        String[] parts = StringUtil.split(path, "/");
        return "/" + wrap(parts).transform(new Func<String, String>(){
            @Override public String map(String i) {
                return StringUtil.dashify(i);
            }
        }).join("/");
    }
    
    private String getFilePath(String path) {
        return "/files/" + path;
    }
    
    //<editor-fold desc="Public Client Methods">
    
    public void addSuites(final String jobId, Suite[] suites) {
        _jsonClient.post(getRpcPath("suites/add"), new HashMap<String, Object>(){{
            put("jobId", jobId);
        }}, suites);
    }
    
    public void registerJobAgent(JobAgent agent) {
        _jsonClient.post(getRpcPath("agents/register"), agent);
    }
    
    public void registerJobClient(JobClient client) {
        _jsonClient.post(getRpcPath("clients/register"), client);
    }
    
    public JobAgentMessage getAgentMessage(final String agentId, JobServerMessage message) {
        return _jsonClient.post(getRpcPath("messages/agents"), new HashMap<String, Object>(){{
            put("agentId", agentId);
        }}, message, JobAgentMessage.class);
    }
    
    public JobClientMessage getClientMessage(final String uiId, JobServerMessage message) {
        return _jsonClient.post(getRpcPath("messages/clients"), new HashMap<String, Object>(){{
            put("clientId", uiId);
        }}, message, JobClientMessage.class);
    }
    
    public void sendServerMessage(JobServerMessage message) {
        _jsonClient.post(getRpcPath("messages/server"), message);
    }
    
    public List<JobAgent> listConnectedAgents() {
        return _jsonClient.get(getRpcPath("agents/list"), 
            new TypeToken<List<JobAgent>>(){}.getType());
    }
    
    public List<Browser> listAvailableBrowsers() {
        return _jsonClient.get(getRpcPath("browsers/list"),
            new TypeToken<List<Browser>>(){}.getType());
    }
    
    public void startJobWorkItem(final String itemId) {
        _jsonClient.get(getRpcPath("jobWorkItems/start"), new HashMap<String, Object>(){{
            put("jobWorkItemId", itemId);
        }});
    }
    
    public void stopJobWorkItem(final String itemId, final boolean success) {
        _jsonClient.get(getRpcPath("jobWorkItems/stop"), new HashMap<String, Object>(){{
            put("jobWorkItemId", itemId);
            put("success", success);
        }});
    }
    
    public void addResults(final String jobWorkItemId, Result[] results) {
        _logger.trace("sending {} results for jobWorkItem : {}", results.length, jobWorkItemId);
        _jsonClient.post(getRpcPath("results/add"),new HashMap<String, Object>(){{
            put("jobWorkItemId", jobWorkItemId);
        }}, results);
    }

    public void addCoverageData(final String jobWorkItemId, String data) {
        _logger.trace("sending coverage data for jobWorkItem : {}", jobWorkItemId);
        _jsonClient.post(getRpcPath("coverage/add"),new HashMap<String, Object>(){{
            put("jobWorkItemId", jobWorkItemId);
        }}, data);
    }

    public List<Suite> getSuites(final String jobId) {
        return _jsonClient.get(getRpcPath("suites/list"), new HashMap<String, Object>(){{
            put("jobId", jobId);
        }}, new TypeToken<List<Suite>>(){}.getType());
    }

    public List<Spec> getSpecs(final String jobId) {
        return _jsonClient.get(getRpcPath("specs/list"), new HashMap<String, Object>(){{
            put("jobId", jobId);
        }}, new TypeToken<List<Spec>>(){}.getType());
    }
    
    public void shutdown() {
        _jsonClient.get(getRpcPath("shutdown"), Object.class);
    }
    //</editor-fold>

    //<editor-fold desc="FILE methods">
    
    public void download(String path, String localPath) {
        download(path, new File(localPath));
    }
    
    public void download(String path, File localFile) {
        _logger.debug("downloading remote path {} to {}", getFilePath(path), localFile);
        download(path, StreamUtil.openFileOutput(localFile));
    }
    
    public void download(String path, FileOutputStream stream) {
        _jsonClient.download(getFilePath(path), stream);
    }

    public String upload(File file, final String fileName) {
        try {
            Map<String, Object> params = null;
            if(!StringUtil.isNullOrEmpty(fileName)) {
                params = new HashMap<String, Object>(){{
                    put("fileName", fileName);
                }};
            }
            InputStream inStream = new FileInputStream(file);
            UploadResponse response = _jsonClient.upload("upload", params, inStream, UploadResponse.class);
            return response.getFileName();
        } catch (FileNotFoundException ex) {
            throw BasicException.raise(ex);
        }
    }
    //</editor-fold>
}
