/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */
package com.sencha.command.test;

import com.sencha.command.BaseSenchaCommand;
import com.sencha.exceptions.ExState;
import com.sencha.logging.SenchaLogManager;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.handler.HandlerCollection;
import org.eclipse.jetty.server.nio.SelectChannelConnector;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.slf4j.Logger;

public class CoverageProxyCommand extends BaseSenchaCommand {
    
    public void setPort(int port) {
        _port = port;
    }
    
    public void setInclude(String include) {
        _includes = include.split(",");
        
    }
    
    public void execute() {
        try {
            Server server = new Server();
            SelectChannelConnector connector = new SelectChannelConnector();
            connector.setPort(_port);
            server.addConnector(connector);
    
            HandlerCollection handlers = new HandlerCollection();
            server.setHandler(handlers);
    
            CoverageProxyServlet.setIncludes(_includes);
            ServletContextHandler context = new ServletContextHandler(handlers, "/", ServletContextHandler.SESSIONS);
            ServletHolder proxyServlet = new ServletHolder(CoverageProxyServlet.class);
            context.addServlet(proxyServlet, "/*");

            _logger.info("Coverage proxy started");
            server.start();
        } catch (Exception e) {
            throw new ExState(e);
        }
    }

    private static final Logger _logger = SenchaLogManager.getLogger();
    private int _port = 8888;
    private String[] _includes = { "src/" };
    
}
