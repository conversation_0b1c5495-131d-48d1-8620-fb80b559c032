<ivy-module version="2.0"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation=
                   "http://www.jayasoft.org/misc/ivy/samples/ivy.xsd"
                xmlns:m="http://ant.apache.org/ivy/maven">
    
    <info organisation="com.sencha" module="sencha-command-test"/>

    <configurations>
        <conf name="compile" description="Required to compile application"/>
        <conf name="runtime" description="Additional run-time dependencies" extends="compile"/>
        <conf name="test"    description="Required for test only" extends="runtime"/>
    </configurations>

    <dependencies>
        <dependency org="org.slf4j" name="slf4j-api" rev="1.6.6" conf="compile->default"/>
        <dependency org="org.apache.ant" name="ant" rev="1.8.4" conf="compile->default">
            <exclude org="commons-collections" module="commons-collections"/>
        </dependency>
        <dependency org="com.google.code.gson" name="gson" rev="2.11.0" conf="compile->default"/>
        <dependency org="org.seleniumhq.selenium" name="selenium-java" rev="2.39.0" conf="runtime->default">
            <exclude org="commons-collections" module="commons-collections"/>
            <exclude org="commons-io" module="commons-io"/>
        </dependency>
        <dependency org="org.seleniumhq.selenium" name="selenium-server" rev="2.39.0" conf="runtime->default">
            <exclude org="commons-collections" module="commons-collections"/>
            <exclude org="commons-io" module="commons-io"/>
        </dependency>
        <dependency org="com.opera" name="operadriver" rev="1.5" conf="runtime->default"/>
        <dependency org="org.apache.commons" name="commons-collections4" rev="4.5.0-M2" force="true"/>
        <dependency org="commons-io" name="commons-io" rev="2.18.0" force="true"/>
    </dependencies>
</ivy-module>