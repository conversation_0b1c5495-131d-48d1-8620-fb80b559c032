var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.arrayIteratorImpl=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}};$jscomp.arrayIterator=function(a){return{next:$jscomp.arrayIteratorImpl(a)}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.SIMPLE_FROUND_POLYFILL=!1;$jscomp.ISOLATE_POLYFILLS=!1;$jscomp.FORCE_POLYFILL_PROMISE=!1;$jscomp.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};$jscomp.getGlobal=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");};$jscomp.global=$jscomp.getGlobal(this);
$jscomp.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");$jscomp.TRUST_ES6_POLYFILLS=!$jscomp.ISOLATE_POLYFILLS||$jscomp.IS_SYMBOL_NATIVE;$jscomp.polyfills={};$jscomp.propertyToPolyfillSymbol={};$jscomp.POLYFILL_PREFIX="$jscp$";var $jscomp$lookupPolyfilledValue=function(a,b){var c=$jscomp.propertyToPolyfillSymbol[b];if(null==c)return a[b];c=a[c];return void 0!==c?c:a[b]};
$jscomp.polyfill=function(a,b,c,d){b&&($jscomp.ISOLATE_POLYFILLS?$jscomp.polyfillIsolated(a,b,c,d):$jscomp.polyfillUnisolated(a,b,c,d))};$jscomp.polyfillUnisolated=function(a,b,c,d){c=$jscomp.global;a=a.split(".");for(d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))return;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&$jscomp.defineProperty(c,a,{configurable:!0,writable:!0,value:b})};
$jscomp.polyfillIsolated=function(a,b,c,d){var e=a.split(".");a=1===e.length;d=e[0];d=!a&&d in $jscomp.polyfills?$jscomp.polyfills:$jscomp.global;for(var f=0;f<e.length-1;f++){var g=e[f];if(!(g in d))return;d=d[g]}e=e[e.length-1];c=$jscomp.IS_SYMBOL_NATIVE&&"es6"===c?d[e]:null;b=b(c);null!=b&&(a?$jscomp.defineProperty($jscomp.polyfills,e,{configurable:!0,writable:!0,value:b}):b!==c&&(void 0===$jscomp.propertyToPolyfillSymbol[e]&&(c=1E9*Math.random()>>>0,$jscomp.propertyToPolyfillSymbol[e]=$jscomp.IS_SYMBOL_NATIVE?
$jscomp.global.Symbol(e):$jscomp.POLYFILL_PREFIX+c+"$"+e),$jscomp.defineProperty(d,$jscomp.propertyToPolyfillSymbol[e],{configurable:!0,writable:!0,value:b})))};$jscomp.initSymbol=function(){};$jscomp.polyfill("Symbol.asyncIterator",function(a){return a?a:Symbol("Symbol.asyncIterator")},"es9","es3");$jscomp.iteratorPrototype=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a};
$jscomp.makeIterator=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):$jscomp.arrayIterator(a)};$jscomp.underscoreProtoCanBeSet=function(){var a={a:!0},b={};try{return b.__proto__=a,b.a}catch(c){}return!1};
$jscomp.setPrototypeOf=$jscomp.TRUST_ES6_POLYFILLS&&"function"==typeof Object.setPrototypeOf?Object.setPrototypeOf:$jscomp.underscoreProtoCanBeSet()?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null;$jscomp.generator={};$jscomp.generator.ensureIteratorResultIsObject_=function(a){if(!(a instanceof Object))throw new TypeError("Iterator result "+a+" is not an object");};
$jscomp.generator.Context=function(){this.isRunning_=!1;this.yieldAllIterator_=null;this.yieldResult=void 0;this.nextAddress=1;this.finallyAddress_=this.catchAddress_=0;this.finallyContexts_=this.abruptCompletion_=null};$jscomp.generator.Context.prototype.start_=function(){if(this.isRunning_)throw new TypeError("Generator is already running");this.isRunning_=!0};$jscomp.generator.Context.prototype.stop_=function(){this.isRunning_=!1};
$jscomp.generator.Context.prototype.jumpToErrorHandler_=function(){this.nextAddress=this.catchAddress_||this.finallyAddress_};$jscomp.generator.Context.prototype.next_=function(a){this.yieldResult=a};$jscomp.generator.Context.prototype.throw_=function(a){this.abruptCompletion_={exception:a,isException:!0};this.jumpToErrorHandler_()};$jscomp.generator.Context.prototype.return=function(a){this.abruptCompletion_={return:a};this.nextAddress=this.finallyAddress_};
$jscomp.generator.Context.prototype.jumpThroughFinallyBlocks=function(a){this.abruptCompletion_={jumpTo:a};this.nextAddress=this.finallyAddress_};$jscomp.generator.Context.prototype.yield=function(a,b){this.nextAddress=b;return{value:a}};$jscomp.generator.Context.prototype.yieldAll=function(a,b){a=$jscomp.makeIterator(a);var c=a.next();$jscomp.generator.ensureIteratorResultIsObject_(c);if(c.done)this.yieldResult=c.value,this.nextAddress=b;else return this.yieldAllIterator_=a,this.yield(c.value,b)};
$jscomp.generator.Context.prototype.jumpTo=function(a){this.nextAddress=a};$jscomp.generator.Context.prototype.jumpToEnd=function(){this.nextAddress=0};$jscomp.generator.Context.prototype.setCatchFinallyBlocks=function(a,b){this.catchAddress_=a;void 0!=b&&(this.finallyAddress_=b)};$jscomp.generator.Context.prototype.setFinallyBlock=function(a){this.catchAddress_=0;this.finallyAddress_=a||0};$jscomp.generator.Context.prototype.leaveTryBlock=function(a,b){this.nextAddress=a;this.catchAddress_=b||0};
$jscomp.generator.Context.prototype.enterCatchBlock=function(a){this.catchAddress_=a||0;a=this.abruptCompletion_.exception;this.abruptCompletion_=null;return a};$jscomp.generator.Context.prototype.enterFinallyBlock=function(a,b,c){c?this.finallyContexts_[c]=this.abruptCompletion_:this.finallyContexts_=[this.abruptCompletion_];this.catchAddress_=a||0;this.finallyAddress_=b||0};
$jscomp.generator.Context.prototype.leaveFinallyBlock=function(a,b){b=this.finallyContexts_.splice(b||0)[0];if(b=this.abruptCompletion_=this.abruptCompletion_||b){if(b.isException)return this.jumpToErrorHandler_();void 0!=b.jumpTo&&this.finallyAddress_<b.jumpTo?(this.nextAddress=b.jumpTo,this.abruptCompletion_=null):this.nextAddress=this.finallyAddress_}else this.nextAddress=a};$jscomp.generator.Context.prototype.forIn=function(a){return new $jscomp.generator.Context.PropertyIterator(a)};
$jscomp.generator.Context.PropertyIterator=function(a){this.object_=a;this.properties_=[];for(var b in a)this.properties_.push(b);this.properties_.reverse()};$jscomp.generator.Context.PropertyIterator.prototype.getNext=function(){for(;0<this.properties_.length;){var a=this.properties_.pop();if(a in this.object_)return a}return null};$jscomp.generator.Engine_=function(a){this.context_=new $jscomp.generator.Context;this.program_=a};
$jscomp.generator.Engine_.prototype.next_=function(a){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_.next,a,this.context_.next_);this.context_.next_(a);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.return_=function(a){this.context_.start_();var b=this.context_.yieldAllIterator_;if(b)return this.yieldAllStep_("return"in b?b["return"]:function(c){return{value:c,done:!0}},a,this.context_.return);this.context_.return(a);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.throw_=function(a){this.context_.start_();if(this.context_.yieldAllIterator_)return this.yieldAllStep_(this.context_.yieldAllIterator_["throw"],a,this.context_.next_);this.context_.throw_(a);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.yieldAllStep_=function(a,b,c){try{var d=a.call(this.context_.yieldAllIterator_,b);$jscomp.generator.ensureIteratorResultIsObject_(d);if(!d.done)return this.context_.stop_(),d;var e=d.value}catch(f){return this.context_.yieldAllIterator_=null,this.context_.throw_(f),this.nextStep_()}this.context_.yieldAllIterator_=null;c.call(this.context_,e);return this.nextStep_()};
$jscomp.generator.Engine_.prototype.nextStep_=function(){for(;this.context_.nextAddress;)try{var a=this.program_(this.context_);if(a)return this.context_.stop_(),{value:a.value,done:!1}}catch(b){this.context_.yieldResult=void 0,this.context_.throw_(b)}this.context_.stop_();if(this.context_.abruptCompletion_){a=this.context_.abruptCompletion_;this.context_.abruptCompletion_=null;if(a.isException)throw a.exception;return{value:a.return,done:!0}}return{value:void 0,done:!0}};
$jscomp.generator.Generator_=function(a){this.next=function(b){return a.next_(b)};this.throw=function(b){return a.throw_(b)};this.return=function(b){return a.return_(b)};this[Symbol.iterator]=function(){return this}};$jscomp.generator.createGenerator=function(a,b){b=new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(b));$jscomp.setPrototypeOf&&a.prototype&&$jscomp.setPrototypeOf(b,a.prototype);return b};
$jscomp.asyncExecutePromiseGenerator=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})};$jscomp.asyncExecutePromiseGeneratorFunction=function(a){return $jscomp.asyncExecutePromiseGenerator(a())};$jscomp.asyncExecutePromiseGeneratorProgram=function(a){return $jscomp.asyncExecutePromiseGenerator(new $jscomp.generator.Generator_(new $jscomp.generator.Engine_(a)))};
$jscomp.makeAsyncIterator=function(a){var b=a[Symbol.asyncIterator];return void 0!==b?b.call(a):new $jscomp.AsyncIteratorFromSyncWrapper($jscomp.makeIterator(a))};$jscomp.AsyncIteratorFromSyncWrapper=function(a){this[Symbol.asyncIterator]=function(){return this};this[Symbol.iterator]=function(){return a};this.next=function(b){return Promise.resolve(a.next(b))};void 0!==a["throw"]&&(this["throw"]=function(b){return Promise.resolve(a["throw"](b))});void 0!==a["return"]&&(this["return"]=function(b){return Promise.resolve(a["return"](b))})};
$jscomp.AsyncGeneratorWrapper$ActionEnum={YIELD_VALUE:0,YIELD_STAR:1,AWAIT_VALUE:2};$jscomp.AsyncGeneratorWrapper$ActionRecord=function(a,b){this.action=a;this.value=b};$jscomp.AsyncGeneratorWrapper$GeneratorMethod={NEXT:"next",THROW:"throw",RETURN:"return"};$jscomp.AsyncGeneratorWrapper$ExecutionFrame_=function(a,b,c,d){this.method=a;this.param=b;this.resolve=c;this.reject=d};$jscomp.AsyncGeneratorWrapper$ExecutionNode_=function(a,b){this.frame=a;this.next=b};
$jscomp.AsyncGeneratorWrapper$ExecutionQueue_=function(){this.tail_=this.head_=null};$jscomp.AsyncGeneratorWrapper$ExecutionQueue_.prototype.isEmpty=function(){return null===this.head_};$jscomp.AsyncGeneratorWrapper$ExecutionQueue_.prototype.first=function(){if(this.head_)return this.head_.frame;throw Error("no frames in executionQueue");};$jscomp.AsyncGeneratorWrapper$ExecutionQueue_.prototype.drop=function(){this.head_&&(this.head_=this.head_.next,this.head_||(this.tail_=null))};
$jscomp.AsyncGeneratorWrapper$ExecutionQueue_.prototype.enqueue=function(a){a=new $jscomp.AsyncGeneratorWrapper$ExecutionNode_(a,null);this.tail_?this.tail_.next=a:this.head_=a;this.tail_=a};
$jscomp.AsyncGeneratorWrapper=function(a){this.generator_=a;this.delegate_=null;this.executionQueue_=new $jscomp.AsyncGeneratorWrapper$ExecutionQueue_;this[Symbol.asyncIterator]=function(){return this};var b=this;this.boundHandleDelegateResult_=function(c){b.handleDelegateResult_(c)};this.boundHandleDelegateError_=function(c){b.handleDelegateError_(c)};this.boundRejectAndClose_=function(c){b.rejectAndClose_(c)}};
$jscomp.AsyncGeneratorWrapper.prototype.enqueueMethod_=function(a,b){var c=this;return new Promise(function(d,e){var f=c.executionQueue_.isEmpty();c.executionQueue_.enqueue(new $jscomp.AsyncGeneratorWrapper$ExecutionFrame_(a,b,d,e));f&&c.runFrame_()})};$jscomp.AsyncGeneratorWrapper.prototype.next=function(a){return this.enqueueMethod_($jscomp.AsyncGeneratorWrapper$GeneratorMethod.NEXT,a)};
$jscomp.AsyncGeneratorWrapper.prototype.return=function(a){return this.enqueueMethod_($jscomp.AsyncGeneratorWrapper$GeneratorMethod.RETURN,new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,a))};$jscomp.AsyncGeneratorWrapper.prototype.throw=function(a){return this.enqueueMethod_($jscomp.AsyncGeneratorWrapper$GeneratorMethod.THROW,a)};
$jscomp.AsyncGeneratorWrapper.prototype.runFrame_=function(){if(!this.executionQueue_.isEmpty())try{this.delegate_?this.runDelegateFrame_():this.runGeneratorFrame_()}catch(a){this.rejectAndClose_(a)}};
$jscomp.AsyncGeneratorWrapper.prototype.runGeneratorFrame_=function(){var a=this,b=this.executionQueue_.first();try{var c=this.generator_[b.method](b.param);if(c.value instanceof $jscomp.AsyncGeneratorWrapper$ActionRecord)switch(c.value.action){case $jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE:Promise.resolve(c.value.value).then(function(d){b.resolve({value:d,done:c.done});a.executionQueue_.drop();a.runFrame_()},function(d){b.reject(d);a.executionQueue_.drop();a.runFrame_()}).catch(this.boundRejectAndClose_);
break;case $jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_STAR:a.delegate_=$jscomp.makeAsyncIterator(c.value.value);b.method=$jscomp.AsyncGeneratorWrapper$GeneratorMethod.NEXT;b.param=void 0;a.runFrame_();break;case $jscomp.AsyncGeneratorWrapper$ActionEnum.AWAIT_VALUE:Promise.resolve(c.value.value).then(function(d){b.method=$jscomp.AsyncGeneratorWrapper$GeneratorMethod.NEXT;b.param=d;a.runFrame_()},function(d){b.method=$jscomp.AsyncGeneratorWrapper$GeneratorMethod.THROW;b.param=d;a.runFrame_()}).catch(this.boundRejectAndClose_);
break;default:throw Error("Unrecognized AsyncGeneratorWrapper$ActionEnum");}else b.resolve(c),a.executionQueue_.drop(),a.runFrame_()}catch(d){b.reject(d),a.executionQueue_.drop(),a.runFrame_()}};
$jscomp.AsyncGeneratorWrapper.prototype.runDelegateFrame_=function(){if(!this.delegate_)throw Error("no delegate to perform execution");var a=this.executionQueue_.first();if(a.method in this.delegate_)try{this.delegate_[a.method](a.param).then(this.boundHandleDelegateResult_,this.boundHandleDelegateError_).catch(this.boundRejectAndClose_)}catch(b){this.handleDelegateError_(b)}else this.delegate_=null,this.runFrame_()};
$jscomp.AsyncGeneratorWrapper.prototype.handleDelegateResult_=function(a){var b=this.executionQueue_.first();!0===a.done?(this.delegate_=null,b.method=$jscomp.AsyncGeneratorWrapper$GeneratorMethod.NEXT,b.param=a.value):(b.resolve({value:a.value,done:!1}),this.executionQueue_.drop());this.runFrame_()};
$jscomp.AsyncGeneratorWrapper.prototype.handleDelegateError_=function(a){var b=this.executionQueue_.first();this.delegate_=null;b.method=$jscomp.AsyncGeneratorWrapper$GeneratorMethod.THROW;b.param=a;this.runFrame_()};
$jscomp.AsyncGeneratorWrapper.prototype.rejectAndClose_=function(a){this.executionQueue_.isEmpty()||(this.executionQueue_.first().reject(a),this.executionQueue_.drop());this.delegate_&&"return"in this.delegate_&&(this.delegate_["return"](void 0),this.delegate_=null);this.generator_["return"](void 0);this.runFrame_()};
$jscomp.polyfill("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}},"es7","es3");$jscomp.owns=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};$jscomp.polyfill("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)$jscomp.owns(b,d)&&c.push(b[d]);return c}},"es8","es3");
$jscomp.polyfill("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)$jscomp.owns(b,d)&&c.push([d,b[d]]);return c}},"es8","es3");
$jscomp.polyfill("Object.fromEntries",function(a){return a?a:function(b){var c={};if(!(Symbol.iterator in b))throw new TypeError(""+b+" is not iterable");b=b[Symbol.iterator].call(b);for(var d=b.next();!d.done;d=b.next()){d=d.value;if(Object(d)!==d)throw new TypeError("iterable for fromEntries should yield objects");c[d[0]]=d[1]}return c}},"es_2019","es3");
$jscomp.polyfill("Object.getOwnPropertyDescriptors",function(a){return a?a:function(b){for(var c={},d=Reflect.ownKeys(b),e=0;e<d.length;e++)c[d[e]]=Object.getOwnPropertyDescriptor(b,d[e]);return c}},"es8","es5");$jscomp.checkStringArgs=function(a,b,c){if(null==a)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};
$jscomp.stringPadding=function(a,b){a=void 0!==a?String(a):" ";return 0<b&&a?a.repeat(Math.ceil(b/a.length)).substring(0,b):""};$jscomp.polyfill("String.prototype.padStart",function(a){return a?a:function(b,c){var d=$jscomp.checkStringArgs(this,null,"padStart");return $jscomp.stringPadding(c,b-d.length)+d}},"es8","es3");
$jscomp.polyfill("String.prototype.padEnd",function(a){return a?a:function(b,c){var d=$jscomp.checkStringArgs(this,null,"padStart");return d+$jscomp.stringPadding(c,b-d.length)}},"es8","es3");$jscomp.polyfill("Promise.prototype.finally",function(a){return a?a:function(b){return this.then(function(c){return Promise.resolve(b()).then(function(){return c})},function(c){return Promise.resolve(b()).then(function(){throw c;})})}},"es9","es3");
$jscomp.polyfill("Array.prototype.flat",function(a){return a?a:function(b){b=void 0===b?1:b;for(var c=[],d=0;d<this.length;d++){var e=this[d];Array.isArray(e)&&0<b?(e=Array.prototype.flat.call(e,b-1),c.push.apply(c,e)):c.push(e)}return c}},"es9","es5");$jscomp.polyfill("Array.prototype.flatMap",function(a){return a?a:function(b,c){for(var d=[],e=0;e<this.length;e++){var f=b.call(c,this[e],e,this);Array.isArray(f)?d.push.apply(d,f):d.push(f)}return d}},"es9","es5");
$jscomp.polyfill("String.prototype.trimLeft",function(a){function b(){return this.replace(/^[\s\xa0]+/,"")}return a||b},"es_2019","es3");$jscomp.polyfill("String.prototype.trimStart",function(a){return a||String.prototype.trimLeft},"es_2019","es3");$jscomp.polyfill("String.prototype.trimRight",function(a){function b(){return this.replace(/[\s\xa0]+$/,"")}return a||b},"es_2019","es3");$jscomp.polyfill("String.prototype.trimEnd",function(a){return a||String.prototype.trimRight},"es_2019","es3");
$jscomp.polyfill("Promise.allSettled",function(a){function b(d){return{status:"fulfilled",value:d}}function c(d){return{status:"rejected",reason:d}}return a?a:function(d){var e=this;d=Array.from(d,function(f){return e.resolve(f).then(b,c)});return e.all(d)}},"es_2020","es3");
$jscomp.polyfill("String.prototype.matchAll",function(a){return a?a:function(b){if(b instanceof RegExp&&!b.global)throw new TypeError("RegExp passed into String.prototype.matchAll() must have global tag.");var c=new RegExp(b,b instanceof RegExp?void 0:"g"),d=this,e=!1,f={next:function(){if(e)return{value:void 0,done:!0};var g=c.exec(d);if(!g)return e=!0,{value:void 0,done:!0};""===g[0]&&(c.lastIndex+=1);return{value:g,done:!1}}};f[Symbol.iterator]=function(){return f};return f}},"es_2020","es3");
$jscomp.polyfill("globalThis",function(a){return a||$jscomp.global},"es_2020","es3");$jscomp.polyfill("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}},"es_2021","es3");
$jscomp.objectCreate=$jscomp.ASSUME_ES5||"function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b};$jscomp.inherits=function(a,b){a.prototype=$jscomp.objectCreate(b.prototype);a.prototype.constructor=a;if($jscomp.setPrototypeOf){var c=$jscomp.setPrototypeOf;c(a,b)}else for(c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.superClass_=b.prototype};
$jscomp.polyfill("AggregateError",function(a){if(a)return a;a=function(b,c){c=Error(c);"stack"in c&&(this.stack=c.stack);this.errors=b;this.message=c.message};$jscomp.inherits(a,Error);a.prototype.name="AggregateError";return a},"es_2021","es3");
$jscomp.polyfill("Promise.any",function(a){return a?a:function(b){b instanceof Array||(b=Array.from(b));return Promise.all(b.map(function(c){return Promise.resolve(c).then(function(d){throw d;},function(d){return d})})).then(function(c){throw new AggregateError(c,"All promises were rejected");},function(c){return c})}},"es_2021","es3");
Ext.define("MyAppName.Application",{extend:"Ext.app.Application",name:"MyAppName",launch:function(){console.log("Application launched - Running ES6+ Feature Tests");this.runAllTests()},runAllTests:function(){this.testVariableScoping();this.testArrowFunctions();this.testClasses();this.testEnhancedObjectLiterals();this.testTemplateLiterals();this.testDestructuring();this.testDefaultParameters();this.testRestParameter();this.testSpreadOperator();this.testIteratorsForOf();this.testGenerators();this.testModules();
this.testSet();this.testWeakSet();this.testMap();this.testWeakMap();this.testUnicode();this.testProxies();this.testSymbols();this.testPromises();this.testReflect();this.testBinaryOctal();this.testProperTailCalls();this.testArrayFindMethods();this.testES2016Features();this.testES2017Features();this.testES2018Features();this.testES2019Features();this.testES2020Features();this.testES2021Features();this.testES2022Features();this.testES2023Features();this.testES2024Features()},testVariableScoping:function(){console.log("\n=== 1. Variable Scoping Test ===");
console.log("Inside block:","I am block scoped","I cannot be reassigned");console.log("Variable scoping test completed")},testArrowFunctions:function(){console.log("\n=== 2. Arrow Functions Test ===");var a=[1,2,3,4,5];const b=a.map(c=>2*c);console.log("Doubled:",b);a=a.filter(c=>0===c%2);console.log("Even numbers:",a);a={name:"Test Object",regularFunction:function(){return function(){return this.name}},arrowFunction:function(){return()=>this.name}};console.log("Regular function this:",a.regularFunction()());
console.log("Arrow function this:",a.arrowFunction()())},testClasses:function(){console.log("\n=== 3. Classes Test ===");class a{constructor(d,e){this.name=d;this.species=e}speak(){return`${this.name} makes a sound`}static getSpeciesCount(){return"Many species exist"}}class b extends a{constructor(d,e){super(d,"Canine");this.breed=e}speak(){return`${this.name} barks`}get info(){return`${this.name} is a ${this.breed}`}}const c=new b("Buddy","Golden Retriever");console.log("Dog speaks:",c.speak());
console.log("Dog info:",c.info);console.log("Static method:",a.getSpeciesCount())},testEnhancedObjectLiterals:function(){console.log("\n=== 4. Enhanced Object Literals Test ===");const a={name:"JavaScript",version:"ES6",greet(){return`Hello from ${this.name}`},isJavaScriptAwesome:!0,ES6Features:["classes","arrows","destructuring"]};console.log("Enhanced object:",a);console.log("Greeting:",a.greet());console.log("Computed property:",a.isJavaScriptAwesome)},testTemplateLiterals:function(){console.log("\n=== 5. Template Literals Test ===");
console.log("Template literal:","Hello Developer, you are testing ES6 features!");console.log("Multi-line:","\n            This is a multi-line\n            template literal that\n            preserves formatting\n        ");const a=function(b,...c){return b.reduce((d,e,f)=>d+e+(c[f]?`**${c[f]}**`:""),"")}`User ${"Developer"} is ${"testing ES6 features"}`;console.log("Tagged template:",a)},testDestructuring:function(){console.log("\n=== 6. Destructuring Test ===");const [a,b,...c]=["red","green",
"blue","yellow"];console.log("Array destructuring:",{primary:a,secondary:b,rest:c});const {firstName:d,lastName:e,age:f,address:{city:g}}={firstName:"John",lastName:"Doe",age:30,address:{city:"New York",country:"USA"}};console.log("Object destructuring:",{firstName:d,lastName:e,age:f,city:g});var h=console,k=h.log,{name:l,email:n="no-email"}={name:"Jane"};k.call(h,"Parameter destructuring:",`User: ${l}, Email: ${n}`)},testDefaultParameters:function(){function a(b,c,d){return`${void 0===c?"Hello":
c}, ${void 0===b?"World":b}${void 0===d?"!":d}`}console.log("\n=== 7. Default Parameters Test ===");console.log("No args:",a());console.log("One arg:",a("JavaScript"));console.log("All args:",a("ES6","Welcome","!!!"));console.log("Function default:",function(b,c){return{name:b,role:void 0===c?"user":c}}("Alice"))},testRestParameter:function(){console.log("\n=== 8. Rest Parameter Test ===");console.log("Sum result:",function(a,...b){console.log("First:",a,"Rest:",b);return a+b.reduce((c,d)=>c+d,0)}(1,
2,3,4,5));(function(...a){a.forEach((b,c)=>{console.log(`Arg ${c}:`,b)})})("a","b","c","d")},testSpreadOperator:function(){console.log("\n=== 9. Spread Operator Test ===");console.log("Array spread:",[1,2,3,4,5,6]);const a=Object.assign({},{a:1,b:2},{c:3,d:4},{e:5});console.log("Object spread:",a);console.log("Function spread:",function(b,c,d){return b*c*d}(...[2,3,4]))},testIteratorsForOf:function(){console.log("\n=== 10. Iterators & For..of Test ===");var a=["a","b","c"];console.log("For..of loop:");
for(const b of a)console.log("Value:",b);a={data:[1,2,3,4,5],[Symbol.iterator](){let b=0;return{next:()=>b<this.data.length?{value:this.data[b++],done:!1}:{done:!0}}}};console.log("Custom iterator:");for(const b of a)console.log("Custom value:",b)},testGenerators:function(){console.log("\n=== 11. Generators Test ===");var a=function*(){yield 1;yield 2;yield 3;return"done"}();console.log("Generator next():",a.next());console.log("Generator next():",a.next());console.log("Generator next():",a.next());
console.log("Generator next():",a.next());a=function*(){let [b,c]=[0,1];for(;;)yield b,[b,c]=[c,b+c]}();console.log("Fibonacci sequence:");for(let b=0;10>b;b++)console.log("Fib:",a.next().value)},testModules:function(){console.log("\n=== 12. Modules Test ===");var a={PI:3.14159,square:e=>e*e,cube:e=>e*e*e,default:function(e){return 2*e}};const {PI:b,square:c,cube:d}=a;a=a.default;console.log("Module PI:",b);console.log("Module square(5):",c(5));console.log("Module cube(3):",d(3));console.log("Module default(7):",
a(7))},testSet:function(){console.log("\n=== 13. Set Test ===");var a=new Set;a.add(1);a.add(2);a.add(2);a.add("hello");console.log("Set size:",a.size);console.log("Set has 2:",a.has(2));console.log("Set values:");for(var b of a)console.log("Set value:",b);b=new Set([1,2,3]);const c=new Set([3,4,5]);a=new Set([...b,...c]);b=new Set([...b].filter(d=>c.has(d)));console.log("Union:",[...a]);console.log("Intersection:",[...b])},testWeakSet:function(){console.log("\n=== 14. WeakSet Test ===");const a=
new WeakSet,b={name:"Object 1"},c={name:"Object 2"};a.add(b);a.add(c);console.log("WeakSet has obj1:",a.has(b));console.log("WeakSet has obj2:",a.has(c));a.delete(b);console.log("After delete, WeakSet has obj1:",a.has(b));console.log("WeakSet is not enumerable and holds weak references")},testMap:function(){console.log("\n=== 15. Map Test ===");const a=new Map,b={id:1};a.set("string","value for string key");a.set(42,"value for number key");a.set(b,"value for object key");a.set(function(){},"value for function key");
console.log("Map size:",a.size);console.log("String key:",a.get("string"));console.log("Number key:",a.get(42));console.log("Object key:",a.get(b));console.log("Map entries:");for(const c of a){const [d,e]=c;console.log("Key:",d,"Value:",e)}},testWeakMap:function(){console.log("\n=== 16. WeakMap Test ===");const a=new WeakMap,b={name:"Key Object 1"},c={name:"Key Object 2"};a.set(b,"Value for obj1");a.set(c,"Value for obj2");console.log("WeakMap get obj1:",a.get(b));console.log("WeakMap has obj2:",
a.has(c));a.delete(b);console.log("After delete, WeakMap has obj1:",a.has(b));console.log("WeakMap holds weak references and is not enumerable")},testUnicode:function(){console.log("\n=== 17. Unicode Test ===");console.log("Unicode hearts:","\ud83d\udc9c","\ud83d\ude0a");console.log("String length:",6);console.log("Code point length:",1);console.log("Strings equal:",!1);console.log("Normalized equal:","caf\u00e9".normalize()==="cafe\u0301".normalize());console.log("Unicode iteration:");for(const a of"\ud83c\udf1f\u2b50\u2728")console.log("Char:",
a)},testProxies:function(){console.log("\n=== 18. Proxies Test ===");const a={name:"Original Object",value:42},b=new Proxy(a,{get(c,d){console.log(`Getting property: ${d}`);return d in c?c[d]:`Property ${d} doesn't exist`},set(c,d,e){console.log(`Setting ${d} = ${e}`);c[d]=e;return!0}});console.log("Proxy get name:",b.name);console.log("Proxy get nonexistent:",b.nonexistent);b.newProperty="New Value";console.log("Target after proxy set:",a)},testSymbols:function(){console.log("\n=== 19. Symbols Test ===");
Symbol();var a=Symbol("description"),b=Symbol("description");console.log("Symbol equality:",a===b);console.log("Symbol description:",a.toString());a={};b=Symbol("privateKey");a[b]="private value";a.publicKey="public value";console.log("Object keys:",Object.keys(a));console.log("Symbol value:",a[b]);console.log("Symbol.iterator example:",[...{data:[1,2,3],[Symbol.iterator](){let c=0;return{next:()=>c<this.data.length?{value:this.data[c++],done:!1}:{done:!0}}}}])},testPromises:function(){console.log("\n=== 20. Promises Test ===");
(new Promise((b,c)=>{setTimeout(()=>{b("Promise resolved!")},100)})).then(b=>{console.log("Promise result:",b)});Promise.resolve(5).then(b=>2*b).then(b=>b+3).then(b=>{console.log("Promise chain result:",b)});const a=[Promise.resolve(1),Promise.resolve(2),Promise.resolve(3)];Promise.all(a).then(b=>{console.log("Promise.all results:",b)});Promise.reject("Error occurred").catch(b=>{console.log("Promise error caught:",b)})},testReflect:function(){console.log("\n=== 21. Reflect Test ===");var a={name:"Test Object",
getValue(){return this.name}};console.log("Reflect.has:",Reflect.has(a,"name"));console.log("Reflect.get:",Reflect.get(a,"name"));Reflect.set(a,"age",25);console.log("After Reflect.set:",a);const b=Reflect.ownKeys(a);console.log("Reflect.ownKeys:",b);a=new Proxy(a,{get(c,d,e){console.log(`Reflect proxy getting: ${d}`);return Reflect.get(c,d,e)}});console.log("Reflect with proxy:",a.name)},testBinaryOctal:function(){console.log("\n=== 22. Binary and Octal Test ===");console.log("Binary 0b1010:",10);
console.log("Octal 0o755:",493);console.log("Number 42 in binary:",(42).toString(2));console.log("Number 42 in octal:",(42).toString(8));console.log("Number 42 in hex:",(42).toString(16));console.log("Parse binary:",10);console.log("Parse octal:",493);console.log("Parse hex:",42)},testProperTailCalls:function(){function a(c,d){d=void 0===d?1:d;return 1>=c?d:a(c-1,c*d)}function b(c){return 1>=c?1:c*b(c-1)}console.log("\n=== 23. Proper Tail Calls Test ===");console.log("Factorial with tail call:",a(5));
console.log("Factorial non-tail call:",b(5));console.log("Note: Tail call optimization depends on strict mode and engine support")},testArrayFindMethods:function(){console.log("\n=== 24. Array Find Methods Test ===");const a=[1,5,10,15,20,25];var b=[{name:"Alice",age:25},{name:"Bob",age:30},{name:"Charlie",age:35}],c=a.find(d=>10<d);console.log("Array.find (>10):",c);c=b.find(d=>30===d.age);console.log("Array.find person:",c);c=a.findIndex(d=>10<d);console.log("Array.findIndex (>10):",c);b=b.findIndex(d=>
"Charlie"===d.name);console.log("Array.findIndex Charlie:",b);b=a.find(d=>100<d);c=a.findIndex(d=>100<d);console.log("Not found value:",b);console.log("Not found index:",c);Array.prototype.includes&&(console.log("Array.includes(15):",a.includes(15)),console.log("Array.includes(100):",a.includes(100)))},testES2016Features:function(){console.log("\n\ud83d\ude80 === ES2016/ES7 FEATURES ===");this.testES2016ArrayIncludes();this.testES2016ExponentiationOperator()},testES2016ArrayIncludes:function(){console.log("\n=== ES2016: Array.includes ===");
const a=[1,2,3,NaN,5],b=["apple","banana","orange"];console.log("Array includes 3:",a.includes(3));console.log("Array includes 4:",a.includes(4));console.log("Array includes NaN:",a.includes(NaN));console.log("Array includes from index 2:",a.includes(1,2));console.log("Fruits includes apple:",b.includes("apple"));console.log("Fruits includes grape:",b.includes("grape"));console.log("indexOf vs includes with NaN:");console.log("indexOf NaN:",a.indexOf(NaN));console.log("includes NaN:",a.includes(NaN))},
testES2016ExponentiationOperator:function(){console.log("\n=== ES2016: Exponentiation Operator (**) ===");console.log("2 ** 3 =",Math.pow(2,3));console.log("2 ** 10 =",Math.pow(2,10));console.log("3 ** 4 =",Math.pow(3,4));console.log("** vs Math.pow:");console.log("5 ** 2 =",Math.pow(5,2));console.log("Math.pow(5, 2) =",Math.pow(5,2));console.log("2 **= 8 result:",Math.pow(2,8));console.log("9 ** 0.5 (square root):",Math.pow(9,.5));console.log("8 ** (1/3) (cube root):",Math.pow(8,1/3));console.log("2 ** -3 =",
Math.pow(2,-3))},testES2017Features:function(){console.log("\n\ud83d\ude80 === ES2017/ES8 FEATURES ===");this.testES2017AsyncFunctions();this.testES2017ObjectValues();this.testES2017ObjectEntries();this.testES2017ObjectPropertyDescriptors();this.testES2017StringPadding();this.testES2017TrailingCommas()},testES2017AsyncFunctions:function(){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){function a(){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){return new Promise(d=>
{setTimeout(()=>d("Data fetched!"),100)})})}function b(){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){if(.5<Math.random())throw Error("Random error occurred");return"Success!"})}console.log("\n=== ES2017: Async Functions ===");try{var c=yield a();console.log("Async result:",c)}catch(d){console.log("Async error:",d)}console.log("Async arrow result:",yield(()=>$jscomp.asyncExecutePromiseGeneratorFunction(function*(){yield new Promise(d=>setTimeout(d,50));return"Arrow async complete"}))());
c=[new Promise(d=>setTimeout(()=>d("Op 1"),50)),new Promise(d=>setTimeout(()=>d("Op 2"),30)),new Promise(d=>setTimeout(()=>d("Op 3"),40))];c=yield Promise.all(c);console.log("Multiple async results:",c);try{const d=yield b();console.log("Maybe error result:",d)}catch(d){console.log("Caught error:",d.message)}})},testES2017ObjectValues:function(){console.log("\n=== ES2017: Object.values ===");const a={name:"John",age:30,city:"New York"},b=Object.values(a);console.log("Object values:",b);console.log("Array values:",
Object.values(["a","b","c"]));console.log("String values:",Object.values("hello"));console.log("Object.keys:",Object.keys(a));console.log("Object.values:",Object.values(a))},testES2017ObjectEntries:function(){console.log("\n=== ES2017: Object.entries ===");var a={host:"localhost",port:3E3,protocol:"https"},b=Object.entries(a);console.log("Object entries:",b);b=Object.fromEntries(b);console.log("Reconstructed object:",b);console.log("Iterating entries:");for(const c of Object.entries(a)){const [d,
e]=c;console.log(`${d}: ${e}`)}a=Object.fromEntries(Object.entries({a:1,b:2,c:3}).map(c=>{var [d,e]=c;return[d,2*e]}));console.log("Doubled values:",a)},testES2017ObjectPropertyDescriptors:function(){console.log("\n=== ES2017: Object.getOwnPropertyDescriptors ===");var a={};Object.defineProperty(a,"name",{value:"Test Object",writable:!1,enumerable:!0,configurable:!0});Object.defineProperty(a,"hidden",{value:"Secret",writable:!0,enumerable:!1,configurable:!0});const b=Object.getOwnPropertyDescriptors(a);
console.log("Property descriptors:",b);a=Object.create(Object.getPrototypeOf(a),Object.getOwnPropertyDescriptors(a));console.log("Cloned object:",a);console.log("Clone name:",a.name);a=Object.getOwnPropertyDescriptors({_value:0,get value(){return this._value},set value(c){this._value=2*c}});console.log("Accessor descriptors:",a.value)},testES2017StringPadding:function(){console.log("\n=== ES2017: String Padding ===");console.log("padStart(10):","hello".padStart(10));console.log('padStart(10, "."):',
"hello".padStart(10,"."));console.log("padStart(3):","hello".padStart(3));console.log("padEnd(10):","hello".padEnd(10));console.log('padEnd(10, "!"):',"hello".padEnd(10,"!"));console.log("padEnd(3):","hello".padEnd(3));console.log("Aligned numbers:");[1,22,333,4444].forEach(a=>{console.log(a.toString().padStart(6,"0"))});console.log("Simple table:");[{name:"Alice",score:95},{name:"Bob",score:87},{name:"Charlie",score:92}].forEach(a=>{console.log(`${a.name.padEnd(10)} ${a.score.toString().padStart(3)}`)})},
testES2017TrailingCommas:function(){console.log("\n=== ES2017: Trailing Commas ===");console.log("Array with trailing comma:",["apple","banana","orange"]);console.log("Object with trailing comma:",{name:"John",age:30,city:"New York"});console.log("Function with trailing comma:","Hello, World!");console.log("Function call with trailing comma:",3)},testES2018Features:function(){console.log("\n\ud83d\ude80 === ES2018/ES9 FEATURES ===");this.testES2018AsyncIterators();this.testES2018ObjectRestSpread();
this.testES2018PromiseFinally()},testES2018AsyncIterators:function(){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){function a(){return new $jscomp.AsyncGeneratorWrapper(function*(){yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.AWAIT_VALUE,Promise.resolve(1)));yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,
yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.AWAIT_VALUE,Promise.resolve(2)));yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.AWAIT_VALUE,Promise.resolve(3)))}())}function b(){return new $jscomp.AsyncGeneratorWrapper(function*(){yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,
"First");yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,"Second");throw Error("Async error");}())}console.log("\n=== ES2018: Async Iterators ===");console.log("Async generator results:");for(var c=$jscomp.makeAsyncIterator(a());;){var d=yield c.next();if(d.done)break;console.log("Async value:",d.value)}c={[Symbol.asyncIterator](){return new $jscomp.AsyncGeneratorWrapper(function*(){for(let e=1;3>=e;e++)yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.AWAIT_VALUE,
new Promise(f=>setTimeout(f,50))),yield new $jscomp.AsyncGeneratorWrapper$ActionRecord($jscomp.AsyncGeneratorWrapper$ActionEnum.YIELD_VALUE,`Item ${e}`)}())}};console.log("Custom async iterable:");for(c=$jscomp.makeAsyncIterator(c);;){d=yield c.next();if(d.done)break;console.log("Async item:",d.value)}try{for(const e=$jscomp.makeAsyncIterator(b());;){const f=yield e.next();if(f.done)break;console.log("Error gen value:",f.value)}}catch(e){console.log("Caught async error:",e.message)}})},testES2018ObjectRestSpread:function(){console.log("\n=== ES2018: Object Rest/Spread ===");
var a={name:"John",age:30,city:"New York",country:"USA"},b=Object.assign({},a),c=a.name;a=a.age;b=(delete b.name,delete b.age,b);console.log("Name:",c);console.log("Age:",a);console.log("Rest:",b);c=Object.assign({},{name:"Alice",age:25},{city:"Boston",job:"Developer"});console.log("Spread combined:",c);c=Object.assign({},{a:1,b:2,c:3},{b:20,d:4});console.log("Override with spread:",c);c={user:{name:"Bob",age:28},settings:{theme:"dark",notifications:!0}};c=Object.assign({},c,{user:Object.assign({},
c.user,{age:29})});console.log("Nested spread:",c);b={name:"Charlie",age:35,city:"Seattle",job:"Designer"};c=Object.assign({},b);b=b.name;c=(delete c.name,c);console.log("Processing user:",b);console.log("Other details:",c)},testES2018PromiseFinally:function(){console.log("\n=== ES2018: Promise.finally ===");const a=b=>new Promise((c,d)=>{setTimeout(()=>{b?c("Request successful"):d(Error("Request failed"))},100)});a(!0).then(b=>{console.log("Success:",b)}).catch(b=>{console.log("Error:",b.message)}).finally(()=>
{console.log("Finally: Loading complete (success case)")});setTimeout(()=>{a(!1).then(b=>{console.log("Success:",b)}).catch(b=>{console.log("Error:",b.message)}).finally(()=>{console.log("Finally: Cleanup complete (error case)")})},200);setTimeout(()=>$jscomp.asyncExecutePromiseGeneratorFunction(function*(){try{const b=yield a(!0);console.log("Async success:",b)}catch(b){console.log("Async error:",b.message)}finally{console.log("Finally: Async cleanup complete")}}),300)},testES2019Features:function(){console.log("\n\ud83d\ude80 === ES2019/ES10 FEATURES ===");
this.testES2019ArrayFlat();this.testES2019ObjectFromEntries();this.testES2019StringTrim();this.testES2019SymbolDescription();this.testES2019OptionalCatch();this.testES2019ArrayStableSort()},testES2019ArrayFlat:function(){console.log("\n=== ES2019: Array.flat & flatMap ===");var a=[1,[2,3],[4,[5,6]]];console.log("Original nested:",a);console.log("Flat (depth 1):",a.flat());console.log("Flat (depth 2):",a.flat(2));console.log("Flat (Infinity):",a.flat(Infinity));var b=[1,2,3,4];a=b.flatMap(d=>[d,2*
d]);console.log("FlatMap result:",a);const c=["Hello world","How are you","Fine thanks"].flatMap(d=>d.split(" "));console.log("Words from sentences:",c);b=b.map(d=>[d,2*d]).flat();console.log("Map then flat:",b);console.log("Same as flatMap:",JSON.stringify(a)===JSON.stringify(b));a=[1,[2,[3,[4,[5]]]]];console.log("Complex nested:",a);console.log("Completely flattened:",a.flat(Infinity))},testES2019ObjectFromEntries:function(){console.log("\n=== ES2019: Object.fromEntries ===");var a=Object.fromEntries([["name",
"John"],["age",30],["city","Boston"]]);console.log("From entries:",a);a=Object.fromEntries(Object.entries({a:1,b:2,c:3}));console.log("Round trip:",a);a=Object.fromEntries(Object.entries({math:95,science:87,english:92}).map(b=>{var [c,d]=b;return[c,d/100]}));console.log("Normalized scores:",a);a=new Map([["x",10],["y",20],["z",30]]);a=Object.fromEntries(a);console.log("From Map:",a);a=Object.fromEntries(Object.entries({name:"Alice",age:25,password:"secret",email:"<EMAIL>"}).filter(b=>{[b]=
b;return"password"!==b}));console.log("Filtered object:",a)},testES2019StringTrim:function(){console.log("\n=== ES2019: String trimStart & trimEnd ===");console.log('Original: "   Hello World   "');console.log('trimStart(): "'+"   Hello World   ".trimStart()+'"');console.log('trimEnd(): "'+"   Hello World   ".trimEnd()+'"');console.log('trim(): "Hello World"');console.log('trimLeft(): "'+"   Hello World   ".trimLeft()+'"');console.log('trimRight(): "'+"   Hello World   ".trimRight()+'"');console.log('Whitespace original: "\t\n  Hello  \r\n\t"');
console.log('Whitespace trimStart: "'+"\t\n  Hello  \r\n\t".trimStart()+'"');console.log('Whitespace trimEnd: "'+"\t\n  Hello  \r\n\t".trimEnd()+'"');const a="  <EMAIL>  ".trimStart().trimEnd();console.log('Cleaned email: "'+a+'"')},testES2019SymbolDescription:function(){console.log("\n=== ES2019: Symbol.description ===");var a=Symbol("my symbol");const b=Symbol();console.log("Symbol with description:",a.toString());console.log("Symbol description property:",a.description);console.log("Symbol without description:",
b.description);console.log("toString():",a.toString());console.log("description:",a.description);a=Symbol("test");console.log("Original description:",a.description);console.log("Description after attempt to change:",a.description);console.log("Iterator symbol description:",Symbol.iterator.description);console.log("AsyncIterator description:",Symbol.asyncIterator.description)},testES2019OptionalCatch:function(){function a(c){try{return parseInt(c)}catch(d){return 0}}console.log("\n=== ES2019: Optional Catch Binding ===");
try{throw Error("Traditional error");}catch(c){console.log("Traditional catch:",c.message)}try{JSON.parse("invalid json")}catch(c){console.log("Optional catch: JSON parsing failed")}let b=!1;try{eval("new URLSearchParams()"),b=!0}catch(c){b=!1}console.log("Feature detection result:",b);console.log("Safe parse int:",a("123"));console.log("Safe parse int (invalid):",a("abc"))},testES2019ArrayStableSort:function(){console.log("\n=== ES2019: Array Stable Sort ===");var a=[{name:"Alice",grade:85,id:1},
{name:"Bob",grade:90,id:2},{name:"Charlie",grade:85,id:3},{name:"David",grade:90,id:4},{name:"Eve",grade:85,id:5}];console.log("Original order:",a.map(b=>`${b.name}(${b.grade})`));a=[...a].sort((b,c)=>b.grade-c.grade);console.log("Sorted by grade:",a.map(b=>`${b.name}(${b.grade})`));a=[{value:3,original:"first"},{value:1,original:"A"},{value:3,original:"second"},{value:2,original:"B"},{value:3,original:"third"}];console.log("Items before sort:",a.map(b=>`${b.value}-${b.original}`));a=[...a].sort((b,
c)=>b.value-c.value);console.log("Stable sorted:",a.map(b=>`${b.value}-${b.original}`));console.log("Note: Items with value 3 maintain their original relative order")},testES2020Features:function(){console.log("\n\ud83d\ude80 === ES2020/ES11 FEATURES ===");this.testES2020NullishCoalescing();this.testES2020OptionalChaining();this.testES2020PromiseAllSettled();this.testES2020StringMatchAll();this.testES2020GlobalThis()},testES2020NullishCoalescing:function(){function a(f){f=void 0===f?{}:f;let g,h,
k,l;return{name:null!=(g=f.name)?g:"Anonymous",age:null!=(h=f.age)?h:18,active:null!=(k=f.active)?k:!0,score:null!=(l=f.score)?l:0}}console.log("\n=== ES2020: Nullish Coalescing (??) ===");console.log("Null coalescing name:","Default Name");console.log("Undefined coalescing age:",25);console.log("Zero coalescing score:",0);console.log("Comparison: ?? vs ||");[null,void 0,0,"",!1,NaN].forEach(f=>{console.log(`${f} ?? "default":`,null!=f?f:"default");console.log(`${f} || "default":`,f||"default")});
console.log("User with nullish values:",a({name:null,score:0}));console.log("User with falsy values:",a({name:"",score:0}));let b,c,d;const e=null!=(d=null==(b={api:{timeout:null}})?void 0:null==(c=b.api)?void 0:c.timeout)?d:5E3;console.log("Chained nullish coalescing:",e)},testES2020OptionalChaining:function(){console.log("\n=== ES2020: Optional Chaining (?.) ===");var a={name:"John",address:{street:"123 Main St",city:"Boston"},getEmail:function(){return"<EMAIL>"}},b={name:"Jane"};let c;
console.log("User city:",null==a?void 0:null==(c=a.address)?void 0:c.city);let d;console.log("User without address city:",null==b?void 0:null==(d=b.address)?void 0:d.city);let e,f;console.log("Deep nesting:",null==a?void 0:null==(e=a.address)?void 0:null==(f=e.coordinates)?void 0:f.lat);let g;console.log("User email:",null==a?void 0:null==(g=a.getEmail)?void 0:g.call(a));let h;console.log("Non-existent method:",null==a?void 0:null==(h=a.getNonExistent)?void 0:h.call(a));b=[a,b];let k;console.log("First user name:",
null==b?void 0:null==(k=b[0])?void 0:k.name);let l;console.log("Non-existent user:",null==b?void 0:null==(l=b[10])?void 0:l.name);let n;console.log("Dynamic access:",null==a?void 0:null==(n=a.address)?void 0:n.city);a={data:{users:[{id:1,profile:{avatar:"avatar1.jpg"}},{id:2}]}};var m;let u,v,w;console.log("First user avatar:",null==a?void 0:null==(m=a.data)?void 0:null==(u=m.users)?void 0:null==(v=u[0])?void 0:null==(w=v.profile)?void 0:w.avatar);let x,y,z,A;console.log("Second user avatar:",null==
a?void 0:null==(x=a.data)?void 0:null==(y=x.users)?void 0:null==(z=y[1])?void 0:null==(A=z.profile)?void 0:A.avatar);m={operations:{add:(p,q)=>p+q,multiply:(p,q)=>p*q}};let r,B;console.log("Calculator add:",null==m?void 0:null==(r=m.operations)?void 0:null==(B=r.add)?void 0:B.call(r,5,3));let t,C;console.log("Calculator divide:",null==m?void 0:null==(t=m.operations)?void 0:null==(C=t.divide)?void 0:C.call(t,10,2))},testES2020PromiseAllSettled:function(){console.log("\n=== ES2020: Promise.allSettled ===");
const a=[Promise.resolve("Success 1"),Promise.reject("Error 1"),Promise.resolve("Success 2"),Promise.reject("Error 2"),new Promise(b=>setTimeout(()=>b("Delayed success"),100))];Promise.allSettled(a).then(b=>{console.log("All settled results:");b.forEach((d,e)=>{"fulfilled"===d.status?console.log(`Promise ${e}: fulfilled with`,d.value):console.log(`Promise ${e}: rejected with`,d.reason)});const c=b.filter(d=>"fulfilled"===d.status).length;b=b.filter(d=>"rejected"===d.status).length;console.log(`Summary: ${c} successful, ${b} failed`)});
setTimeout(()=>{Promise.all([Promise.resolve(1),Promise.reject("error"),Promise.resolve(3)]).then(b=>{console.log("Promise.all results:",b)}).catch(b=>{console.log("Promise.all caught error:",b)})},200)},testES2020StringMatchAll:function(){console.log("\n=== ES2020: String.matchAll ===");const a=/\d{4}/g;var b=[..."The year 2020 was followed by 2021 and then 2022".matchAll(a)];console.log("All year matches:",b.map(d=>d[0]));b.forEach((d,e)=>{console.log(`Match ${e}:`,{value:d[0],index:d.index,input:d.input.substring(d.index-
5,d.index+10)})});b=/(\d{4})-(\d{2})-(\d{2})/g;console.log("Date matches with groups:");for(var c of"Today is 2024-06-13 and tomorrow is 2024-06-14".matchAll(b))console.log("Full match:",c[0]),console.log("Year:",c[1],"Month:",c[2],"Day:",c[3]),console.log("---");console.log("Comparison with other methods:");console.log("match() with global:","The year 2020 was followed by 2021 and then 2022".match(a));for(b=[];null!==(c=a.exec("The year 2020 was followed by 2021 and then 2022"));)b.push(c[0]);console.log("exec() loop results:",
b)},testES2020GlobalThis:function(){console.log("\n=== ES2020: globalThis ===");console.log("globalThis type:",typeof globalThis);globalThis.myGlobalVar="Hello from globalThis";console.log("Global variable:",globalThis.myGlobalVar);const a=function(){if("undefined"!==typeof globalThis)return globalThis;if("undefined"!==typeof window)return window;if("undefined"!==typeof global)return global;if("undefined"!==typeof self)return self;throw Error("Unable to locate global object");}();console.log("Global object found:",
typeof a);globalThis||("undefined"!==typeof window?window.globalThis=window:"undefined"!==typeof global?global.globalThis=global:"undefined"!==typeof self&&(self.globalThis=self));console.log("globalThis standardizes global object access across environments")},testES2021Features:function(){console.log("\n\ud83d\ude80 === ES2021/ES12 FEATURES ===");this.testES2021ReplaceAll();this.testES2021PromiseAny();this.testES2021WeakRef();this.testES2021NumericSeparators();this.testES2021LogicalAssignment()},
testES2021ReplaceAll:function(){console.log("\n=== ES2021: String.replaceAll ===");console.log('Replace all "the":',"The quick brown fox jumps over THE lazy dog. The fox is quick.");console.log('Replace "The":',"A quick brown fox jumps over the lazy dog. A fox is quick.");const a="The quick brown fox jumps over the lazy dog. The fox is quick.".replaceAll(/fox/g,"cat");console.log("Regex replace:",a);console.log("replace() only first:","The quick brown fox jumps over THE lazy dog. The fox is quick.");
console.log("replaceAll() all:","The quick brown fox jumps over THE lazy dog. The fox is quick.");console.log("CSV to TSV:\n","name\tage\tcity\nJohn\t30\tBoston\nJane\t25\tSeattle");console.log("Sanitized HTML:",'&lt;script&gt;alert("xss")&lt;/script&gt;Hello &lt;b&gt;world&lt;/b&gt;');console.log("Cleaned URL:","https://example.com?param1=value1&param2=value2&param1=value3")},testES2021PromiseAny:function(){console.log("\n=== ES2021: Promise.any ===");var a=new Promise(d=>setTimeout(()=>d("Fast"),
50));const b=new Promise(d=>setTimeout(()=>d("Slow"),200)),c=Promise.reject("Failed");Promise.any([c,b,a]).then(d=>{console.log("Promise.any result:",d)}).catch(d=>{console.log("Promise.any error:",d)});a=[Promise.reject("Error 1"),Promise.reject("Error 2"),Promise.reject("Error 3")];Promise.any(a).then(d=>{console.log("All failing result:",d)}).catch(d=>{console.log("AggregateError:",d.constructor.name);console.log("All errors:",d.errors)});setTimeout(()=>{const d=[Promise.reject("Reject 1"),Promise.resolve("Resolve 1"),
Promise.resolve("Resolve 2")];console.log("\nComparison of Promise methods:");Promise.any(d).then(e=>console.log("Promise.any:",e)).catch(e=>console.log("Promise.any error:",e));Promise.race(d).then(e=>console.log("Promise.race:",e)).catch(e=>console.log("Promise.race error:",e));Promise.all(d).then(e=>console.log("Promise.all:",e)).catch(e=>console.log("Promise.all error:",e));Promise.allSettled(d).then(e=>console.log("Promise.allSettled:",e.map(f=>f.status)))},300)},testES2021WeakRef:function(){function a(){const g=
c.deref();if(g)return console.log("Target still alive:",g.name),!0;console.log("Target has been garbage collected");return!1}console.log("\n=== ES2021: WeakRef ===");var b={name:"Target Object",data:Array(1E3).fill("data")};const c=new WeakRef(b);console.log("WeakRef created");let d;console.log("Target via WeakRef:",null==(d=c.deref())?void 0:d.name);a();b=null;"function"===typeof gc&&gc();setTimeout(()=>{a()},100);class e{constructor(){this.cache=new Map}set(g,h){this.cache.set(g,new WeakRef(h))}get(g){var h=
this.cache.get(g);if(h){if(h=h.deref())return h;this.cache.delete(g)}}cleanup(){for(const g of this.cache){const [h,k]=g;k.deref()||this.cache.delete(h)}}}const f=new e;b={id:1,data:"Important data"};f.set("obj1",b);console.log("Cached object:",f.get("obj1"));b=null;setTimeout(()=>{console.log("After removing strong reference:",f.get("obj1"));f.cleanup()},200)},testES2021NumericSeparators:function(){console.log("\n=== ES2021: Numeric Separators ===");console.log("Million:",1E6);console.log("Billion:",
1E9);console.log("Trillion:",1E12);console.log("Binary with separators:",41349);console.log("Octal with separators:",134170833);console.log("Hex with separators:",4293713502);console.log("Price:",29999.99);console.log("Pi:",3.141592653589793);console.log("Scientific notation:",1);console.log("Avogadro number:",6.02214076E23);console.log("File size (1GB):",1073741824);console.log("Timeout (5s):",5E3);console.log("Max safe integer:",9007199254740991);console.log("typeof 1_000_000:","number");console.log("1_000_000 === 1000000:",
!0)},testES2021LogicalAssignment:function(){console.log("\n=== ES2021: Logical Assignment Operators ===");var a={name:"John",age:30};a.name&&(a.name=a.name.toUpperCase());console.log("AND assignment (truthy):",a.name);a.email&&(a.email=a.email.toLowerCase());console.log("AND assignment (falsy):",a.email);a={theme:"",timeout:0,debug:!1};a.theme||(a.theme="default");a.timeout||(a.timeout=5E3);a.debug||(a.debug=!0);console.log("OR assignment config:",a);a={name:null,count:0,enabled:!1};null!=a.name||
(a.name="Default Name");null!=a.count||(a.count=10);null!=a.enabled||(a.enabled=!0);null!=a.newProp||(a.newProp="New Value");console.log("Nullish assignment settings:",a);class b{constructor(d){d=void 0===d?{}:d;let e;null!=(e=d).theme||(e.theme="light");let f;null!=(f=d).language||(f.language="en");let g;null!=(g=d).notifications||(g.notifications=!0);let h;null!=(h=d).autoSave||(h.autoSave=!1);this.preferences=d}updatePreference(d,e){let f;(f=this.preferences)[d]&&(f[d]=e)}setDefault(d,e){let f;
(f=this.preferences)[d]||(f[d]=e)}setIfNull(d,e){let f;null!=(f=this.preferences)[d]||(f[d]=e)}}a=new b({theme:"dark",notifications:null});console.log("Initial preferences:",a.preferences);a.updatePreference("theme","blue");a.setDefault("autoSave",!0);a.setIfNull("notifications",!1);console.log("Updated preferences:",a.preferences);a=null;null==a&&(a="default");var c=null;null!=c||(c="default");console.log("Traditional vs modern:",a===c)},testES2022Features:function(){console.log("\n\ud83d\ude80 === ES2022/ES13 FEATURES ===");
this.testES2022TopLevelAwait();this.testES2022ArrayAt();this.testES2022ErrorCause();this.testES2022HasOwn();this.testES2022RegExpMatchIndices()},testES2022TopLevelAwait:function(){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){console.log("\n=== ES2022: Top-level await ===");console.log("Simulating top-level await...");yield(()=>$jscomp.asyncExecutePromiseGeneratorFunction(function*(){const d=yield(yield fetch("/api/config").catch(()=>({json:()=>Promise.resolve({apiUrl:"https://api.example.com",
timeout:5E3})}))).json();console.log("Config loaded at module level:",d);return d}))();const a=yield function(){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){yield new Promise(d=>setTimeout(d,50));return{formatDate:d=>d.toISOString().split("T")[0],capitalize:d=>d.charAt(0).toUpperCase()+d.slice(1)}})}();console.log("Dynamic module loaded:",a.formatDate(new Date));const [b,c]=yield Promise.all([Promise.resolve({name:"John",id:1}),Promise.resolve({theme:"dark",lang:"en"})]);console.log("Parallel top-level awaits:",
{userData:b,settingsData:c});console.log("Benefits: No need for IIFE, cleaner module initialization")})},testES2022ArrayAt:function(){console.log("\n=== ES2022: Array.at() method ===");const a=["apple","banana","cherry","date","elderberry"];console.log("at(0):",a.at(0));console.log("at(2):",a.at(2));console.log("at(-1):",a.at(-1));console.log("at(-2):",a.at(-2));console.log("at(-5):",a.at(-5));console.log("at(10):",a.at(10));console.log("at(-10):",a.at(-10));console.log("\nComparison:");console.log("fruits[0]:",
a[0]);console.log("fruits.at(0):",a.at(0));console.log("fruits[fruits.length - 1]:",a[a.length-1]);console.log("fruits.at(-1):",a.at(-1));const b=new Uint8Array([10,20,30,40,50]);console.log("\nTypedArray.at(-1):",b.at(-1));console.log("String.at(-1):","hello".at(-1));console.log("Last 3 fruits:",function(c,d){return Array.from({length:d},(e,f)=>c.at(-(f+1))).reverse()}(a,3));console.log("2nd from end:",a.at(-2));console.log("3rd from end:",a.at(-3))},testES2022ErrorCause:function(){function a(e){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){try{throw Error("Network timeout");
}catch(f){throw Error(`Failed to fetch user ${e}`,{cause:f});}})}function b(e){try{if(!e.includes("@"))throw Error("Missing @ symbol");}catch(f){throw new d("Email validation failed","email",e,f);}}function c(e){try{throw Error("Payment declined");}catch(f){throw Error("Order processing failed",{cause:{originalError:f,orderId:e.id,timestamp:new Date,retryable:!0}});}}console.log("\n=== ES2022: Error.cause ===");try{try{JSON.parse("{ invalid json }")}catch(e){throw Error("Failed to parse JSON data",
{cause:e});}}catch(e){console.log("Error message:",e.message),console.log("Error cause:",e.cause.message),console.log("Original error type:",e.cause.constructor.name)}(function(e){return $jscomp.asyncExecutePromiseGeneratorFunction(function*(){try{return yield a(e)}catch(f){throw Error("Unable to load user profile",{cause:f});}})})(123).catch(e=>{console.log("\nChained errors:");console.log("Top level:",e.message);console.log("Caused by:",e.cause.message);console.log("Root cause:",e.cause.cause.message)});
class d extends Error{constructor(e,f,g,h){super(e,{cause:h});this.name="ValidationError";this.field=f;this.value=g}}try{b("invalid-email")}catch(e){console.log("\nCustom error with cause:"),console.log("Error type:",e.constructor.name),console.log("Message:",e.message),console.log("Field:",e.field),console.log("Value:",e.value),console.log("Root cause:",e.cause.message)}try{c({id:"ORD-123",amount:99.99})}catch(e){console.log("\nStructured error cause:"),console.log("Error:",e.message),console.log("Order ID:",
e.cause.orderId),console.log("Retryable:",e.cause.retryable),console.log("Original:",e.cause.originalError.message)}},testES2022HasOwn:function(){console.log("\n=== ES2022: Object.hasOwn() ===");const a={name:"JavaScript",version:"ES2022"};console.log('Object.hasOwn(obj, "name"):',Object.hasOwn(a,"name"));console.log('Object.hasOwn(obj, "age"):',Object.hasOwn(a,"age"));const b=Object.create({age:25});b.name="John";console.log('Object.hasOwn(child, "name"):',Object.hasOwn(b,"name"));console.log('Object.hasOwn(child, "age"):',
Object.hasOwn(b,"age"));console.log('Object.hasOwn(obj, "toString"):',Object.hasOwn(a,"toString"));console.log('Object.hasOwn(child, "toString"):',Object.hasOwn(b,"toString"))}});
