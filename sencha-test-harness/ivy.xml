<ivy-module version="2.0"
               xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation=
                   "http://www.jayasoft.org/misc/ivy/samples/ivy.xsd"
                xmlns:m="http://ant.apache.org/ivy/maven">
    
    <info organisation="com.sencha" module="sencha-test-harness"/>

    <configurations>
        <conf name="compile" description="Required to compile application"/>
        <conf name="runtime" description="Additional run-time dependencies" extends="compile"/>
        <conf name="test"    description="Required for test only" extends="runtime"/>
    </configurations>

    <dependencies>
        <dependency org="org.eclipse.aether" name="aether-api" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.eclipse.aether" name="aether-util" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.eclipse.aether" name="aether-impl" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.eclipse.aether" name="aether-connector-basic" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.eclipse.aether" name="aether-transport-file" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.eclipse.aether" name="aether-transport-http" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.eclipse.aether" name="aether-transport-wagon" rev="0.9.0.M4" conf="runtime->default"/>
        <dependency org="org.apache.maven" name="maven-aether-provider" rev="3.1.1" conf="runtime->default"/>
        <dependency org="org.apache.maven.wagon" name="wagon-ssh" rev="1.0" conf="runtime->default"/>
        <dependency org="org.slf4j" name="slf4j-api" rev="1.6.6" conf="compile->default"/>
        <dependency org="junit" name="junit" rev="4.11" conf="compile->default"/>
        <!--<dependency org="com.google.code.gson" name="gson" rev="2.2.4" conf="compile->default"/>-->
    </dependencies>


    <!-- 
    <dependency>
        <groupId>com.jcabi</groupId>
        <artifactId>jcabi-aether</artifactId>
        <version>0.8</version>
    </dependency>
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.6.6</version>
        <scope>provided</scope>
    </dependency>
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
        <version>1.6.6</version>
        <scope>provided</scope>
    </dependency>
    <dependency>
        <groupId>com.sencha.cmd</groupId>
        <artifactId>sencha</artifactId>
        <version>${project.version}</version>
        <scope>compile</scope>
        <exclusions>
            <exclusion>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
            </exclusion>
            <exclusion>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-jdk14</artifactId>
            </exclusion>
        </exclusions>
    </dependency>
    <dependency>
        <groupId>com.sencha.cmd</groupId>
        <artifactId>sencha</artifactId>
        <version>${project.version}</version>
        <scope>runtime</scope>
        <classifier>distribution</classifier>
        <type>zip</type>
    </dependency>
    <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.10</version>
    </dependency>
    -->

</ivy-module>