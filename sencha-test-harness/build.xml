<?xml version="1.0" encoding="UTF-8"?>
<!-- You may freely edit this file. See commented blocks below for -->
<!-- some examples of how to customize the build. -->
<!-- (If you delete it and reopen the project it will be recreated.) -->
<!-- By default, only the Clean and Build commands use this build script. -->
<!-- Commands such as Run, Debug, and Test only use this build script if -->
<!-- the Compile on Save feature is turned off for the project. -->
<!-- You can turn off the Compile on Save (or Deploy on Save) setting -->
<!-- in the project's Project Properties dialog box.-->
<project name="sencha-test-harness" default="default" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">
    <description>Builds, tests, and runs the project sencha-test-harness.</description>
    <import file="nbproject/build-impl.xml"/>
    <!--

    There exist several targets which are by default empty and which can be 
    used for execution of your tasks. These targets are usually executed 
    before and after some main targets. They are: 

      -pre-init:                 called before initialization of project properties
      -post-init:                called after initialization of project properties
      -pre-compile:              called before javac compilation
      -post-compile:             called after javac compilation
      -pre-compile-single:       called before javac compilation of single file
      -post-compile-single:      called after javac compilation of single file
      -pre-compile-test:         called before javac compilation of JUnit tests
      -post-compile-test:        called after javac compilation of JUnit tests
      -pre-compile-test-single:  called before javac compilation of single JUnit test
      -post-compile-test-single: called after javac compilation of single JUunit test
      -pre-jar:                  called before JAR building
      -post-jar:                 called after JAR building
      -post-clean:               called after cleaning build products

    (Targets beginning with '-' are not intended to be called on their own.)

    Example of inserting an obfuscator after compilation could look like this:

        <target name="-post-compile">
            <obfuscate>
                <fileset dir="${build.classes.dir}"/>
            </obfuscate>
        </target>

    For list of available properties check the imported 
    nbproject/build-impl.xml file. 


    Another way to customize the build is by overriding existing main targets.
    The targets of interest are: 

      -init-macrodef-javac:     defines macro for javac compilation
      -init-macrodef-junit:     defines macro for junit execution
      -init-macrodef-debug:     defines macro for class debugging
      -init-macrodef-java:      defines macro for class execution
      -do-jar:                  JAR building
      run:                      execution of project 
      -javadoc-build:           Javadoc generation
      test-report:              JUnit report generation

    An example of overriding the target for project execution could look like this:

        <target name="run" depends="sencha-test-harness-impl.jar">
            <exec dir="bin" executable="launcher.exe">
                <arg file="${dist.jar}"/>
            </exec>
        </target>

    Notice that the overridden target depends on the jar target and not only on 
    the compile target as the regular run target does. Again, for a list of available 
    properties which you can use, check the target you are overriding in the
    nbproject/build-impl.xml file. 

    -->
    
    <target name="-init-ivy">
        <path id="ivy.lib.path">
            <fileset dir="${basedir}/../lib/" includes="ivy-2.3.0.jar"/>
        </path>
        <taskdef resource="org/apache/ivy/ant/antlib.xml"
                 uri="antlib:org.apache.ivy.ant" 
                 classpathref="ivy.lib.path"/>
        <ivy:settings file="${basedir}/../ivysettings.xml"/>
    </target>
    
    <target name="-ivy-retrieve" depends="-init-ivy">
        <local name="property.file"/>
        <property name="property.file" value="${basedir}/nbproject/project.properties"/>
        <ivy:retrieve/> <!-- Load dependencies to the project -->
        <pathconvert property="ivy.classpath.computed" dirsep="/" pathsep=":">
            <path>
                <fileset dir="lib" includes="*.jar"/>
            </path>
            <map from="${basedir}${file.separator}" to=""/>
        </pathconvert>

        <propertyfile file="${property.file}">
            <entry operation="=" key="ivy.classpath" value="${ivy.classpath.computed}"/>
        </propertyfile>
        <copy file="${property.file}" tofile="${property.file}.tmp">
            <filterchain>
                <headfilter lines="-1" skip="1"/>
            </filterchain>
        </copy>
        <move file="${property.file}.tmp" tofile="${property.file}"/>
    </target>
    
    <target name="-pre-compile" depends="-ivy-retrieve"/>
    <target name="-pre-compile-single" depends="-ivy-retrieve"/>
    <target name="-post-clean">
        <delete dir="lib"/>
        <delete dir="${dist.dir}"/>
    </target>    
    
    <target name="-pre-jar">
        <pathconvert pathsep=" " property="jar.ivy.classpath">
            <path path="${ivy.classpath}"/>
            <chainedmapper>
                <flattenmapper/>
                <filtermapper>
                    <replacestring from=" " to="%20"/>
                </filtermapper>
                <globmapper from="*" to="lib/*"/>
            </chainedmapper>
        </pathconvert>    
<property name="jar.classpath" value="${jar.ivy.classpath} lib/Sencha/Cmd/sencha.jar lib/Sencha/Cmd/extensions/cmd-test/sencha-test.jar lib/Sencha/Cmd/extensions/sencha-compass/sencha-compass.jar lib/Sencha/Cmd/extensions/sencha-service/sencha-service.jar lib/Sencha/Cmd/extensions/sencha-fashion/sencha-fashion.jar"/>
    </target>
    
    <target name="-setup-dist-folder">
        <delete dir="${dist.dir}/lib/Sencha/Cmd"/>
        <mkdir dir="${dist.dir}/lib/Sencha/Cmd"/> 
        <copy todir="${dist.dir}/lib/Sencha/Cmd">
            <fileset dir="${basedir}/../sencha-command/dist/SenchaCmd" includes="**/*"/>
        </copy>
        <copy todir="${dist.dir}/lib/Sencha/Cmd/extensions/sencha-compass">
            <fileset dir="${basedir}/../sencha-command-compass/dist" includes="**/*"/>
        </copy>
        <copy todir="${dist.dir}/lib/Sencha/Cmd/extensions/sencha-fashion">
            <fileset dir="${basedir}/../sencha-fashion/dist" includes="**/*"/>
        </copy>
        <copy todir="${dist.dir}/lib/Sencha/Cmd/extensions/sencha-service">
            <fileset dir="${basedir}/../sencha-command-service/dist" includes="**/*"/>
        </copy>
        <copy todir="${dist.dir}/lib/Sencha/Cmd/extensions/cmd-test">
            <fileset dir="${basedir}/../sencha-command-test/dist" includes="**/*"/>
        </copy>
        <delete dir="${dist.dir}/lib/" includes="sencha*.jar"/>
        <ant dir="${basedir}/../" inheritall="false" inheritrefs="false">
            <target name="get-sencha-build-artifacts"/>
            <target name="stage-frameworks"/>
        </ant>
        <delete dir="${dist.dir}/test-apps"/>
        <copy todir="${dist.dir}" overwrite="true">
            <fileset dir="${basedir}/files" includes="**/*"/>
        </copy>
        <propertyfile file="${dist.dir}/lib/Sencha/Cmd/sencha.cfg">
            <entry key="cmd.minver"
                   value="*******"
                   operation="="/>
        </propertyfile>
    </target>
    
    <target name="-post-jar" depends="-setup-dist-folder,-setup-working-dir">
        <property name="work.dir.value" value="${build.dir}/test-temp"/>
        <mkdir dir="${work.dir.value}"/>
    </target>

    <target name="-setup-working-dir">
        <property name="work.dir.value" value="${build.dir}/test-temp"/>
        <script language="javascript">
            project.setProperty("work.dir", project.getProperty("work.dir.value"));
        </script>
        <mkdir dir="${work.dir}"/>
    </target>

    <target name="-pre-test-run-single" 
            depends="-setup-dist-folder,sencha-test-harness-impl.-pre-test-run-single"/>

    <target name="-pre-test-run" 
            depends="-setup-dist-folder,sencha-test-harness-impl.-pre-test-run"/>

    <target name="-pre-test" depends="-setup-dist-folder"/>
    
    <target name="external-test-setup" depends="jar,compile-test" description="setup project for external testing (IDEA)"/>
    <target name="external-test-run"
            depends="external-test-setup,test"/>

    <target name="run-single-test">
        <property name="javac.includes" value="${test.name}"/>
        <property name="test.includes" value="${test.name}"/>
        <ant target="test-single"
             inheritall="true"
             inheritrefs="true"/>
    </target>

</project>
