/*
 * Copyright (c) 2012. Sencha Inc.
 */
package com.sencha.command.fashion;

import com.sencha.command.Sencha;
import com.sencha.logging.SenchaLogManager;
import com.sencha.test.FashionTestBase;
import com.sencha.tools.compiler.FashionScriptTestBase;
import com.sencha.util.FileUtil;
import com.sencha.util.PathUtil;
import com.sencha.util.StringUtil;
import java.io.File;

import org.junit.*;
import org.slf4j.Logger;

import static org.junit.Assert.*;
    
public class FashionCommandTest extends FashionScriptTestBase {
    private static final Logger _logger = SenchaLogManager.getLogger();

    FashionCommand _fashion;

    String _outputFilePath = getSassPath("foo.css");

    @Before
    public void beforeEachTest () {
        Sencha sencha = new Sencha();
        _fashion = (FashionCommand) sencha.findCommand("fashion");
    }

    @Test
    public void testImport () {
        String[] command = new String[] {
                "-useRhino=true",
                getSassPath("foo.scss"),
                _outputFilePath
        };

        _fashion.dispatch(command);
        String baseDir = new File(_outputFilePath).getParent();
        baseDir = PathUtil.convertPathCharsToUnix(baseDir);
        _logger.info("baseDir is {}", baseDir);

        String expected = StringUtil.join(new String[]{
                "/* /foo.scss:1 */",
                ".foo {",
                "    color: red;",
                "}",
                "/* /bar.scss:1 */",
                ".bar {",
                "    color: green;",
                "}",
                "/* /baz.scss:1 */",
                ".baz {",
                "    color: blue;",
                "}",
                "/* /jazz.scss:1 */",
                ".jazz {",
                "    color: orange;",
                "}",
                "/* /pizazz.scss:1 */",
                ".pizazz {",
                "    color: pink;",
                "}"
        }, "\n"),
                actual = FileUtil.readUnicodeFile(_outputFilePath).replace(baseDir, "");

        assertEquals(expected, actual);
    }

    @Test
    public void testImportFailed() {
        StringBuilder consoleOut = new StringBuilder();
        _fashion
                .getFashionHost()
                .getScriptHost()
                .getConsole()
                .captureTo(consoleOut);

        String[] command = new String[] {
                "-useRhino=true",
                getSassPath("fail.scss"),
                _outputFilePath
        };

        _fashion.dispatch(command);

        assertTrue(consoleOut.toString().contains("failed to download path : "));
    }


//    @Test
//    public void testImportCompass() {
//        String[] command = new String[] {
//            getSassPath("import-compass.scss"),
//            _outputFilePath
//        };
//
//        _fashion.dispatch(command);
//        // TODO: call some compass functions/mixins and check the result
//    }
//
//    @Test
//    public void testImportBlueprint() {
//        String[] command = new String[] {
//            getSassPath("import-blueprint.scss"),
//            _outputFilePath
//        };
//
//        _fashion.dispatch(command);
//        // TODO: call some blueprint functions/mixins and check the result
//    }

    protected String getSassPath (String name) {
        return FashionTestBase.getPath("sass" + File.separator + name, 3);
    }

}
