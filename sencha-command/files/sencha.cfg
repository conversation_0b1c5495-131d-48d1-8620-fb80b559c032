# sencha.cfg
#
# This is the main configuration file for Sencha Cmd. The properties defined in
# this file are used to initialize Sencha Cmd and should be edited with some
# caution.
#
# On previous versions, this file provided a way to specify the cmd.jvm.* properties
# to control the execution of the JVM. To accommodate all possible execution scenarios
# support for these properties has been removed in favor of using the _JAVA_OPTIONS
# environment variable.
#

#------------------------------------------------------------------------------
# This indicates the platform that Cmd is installed on.  This is used for
# platform specific package management.
#
# Possible values: windows, osx, linux, linux-x64
#
# cmd.platform=

#------------------------------------------------------------------------------
# This is the Sencha Cmd version.
#
# THIS PROPERTY SHOULD NOT BE MODIFIED.

cmd.version=7.9.0.32

#------------------------------------------------------------------------------
# This indicates the level of backwards compatibility provided. That is to say,
# apps requiring versions between cmd.minver and cmd.version (inclusive) should
# be able to use this version.
#
# THIS PROPERTY SHOULD NOT BE MODIFIED.

cmd.minver=3.0.0.0

#------------------------------------------------------------------------------
# The folder for the local package repository. By default, this folder is shared
# by all versions of Sencha Cmd. In other words, upgrading Sencha Cmd does not
# affect the local repository.

repo.local.dir=${cmd.dir}/../repo

#------------------------------------------------------------------------------
# This is the default port to use for the Sencha Cmd Web Server.

cmd.web.port=1841

#------------------------------------------------------------------------------
# Java System Properties
#
# By setting any "system.*" properties you can set Java System Properties. For
# general information on these, see:
#
# https://openjdk.org/guide
#

#------------------------------------------------------------------------------
# Proxy Settings
#
# The primary reason to set Java System Properties is to handle HTTP Proxies.
# By default, Java uses "http.proxy*" properties to configure HTTP proxies, but
# the "java.net.useSystemProxies" option can be enabled to improve the use of
# system-configured proxy settings based on your platform. If this setting does
# not work for your proxy server setup, try disabling this setting by commenting
# it out and enabling the other settings. See also this information on proxy
# setup:
#
# https://openjdk.org/guide
#
# NOTE: If you find that you need to adjust these settings, you may want to do
# so in a "sencha.cfg" file one folder above this folder. The settings in that
# file override these settings, so be sure to only copy the settings you need
# to that location. The advantage to putting these settings in that location is
# that they will not be "lost" as you upgrade Cmd.

system.java.net.useSystemProxies=true

# These are the legacy options that come in to play when the above is commented
# out:
#system.http.proxyHost=proxy.mycompany.com
#system.http.proxyPort=80
#system.http.proxyUser=username
#system.http.proxyPassword=password

#------------------------------------------------------------------------------
# Merge Tool Settings
#
# To enable use of a visual merge tool to resolve merge conflicts, set the
# following property to the desired merge tool path:
#
#       cmd.merge.tool=p4merge
#
# Next, to configure the arguments for the merge tool, set this property:
#
#       cmd.merge.tool.args={base} {user} {generated} {out}
#
# Alternatively, the arguments for several merge tools are defined below and can
# be used in your configuration for simplicity/clarity like so:
#
#       cmd.merge.tool.args=${cmd.merge.tool.args.sourcegear}
#
# NOTE: the cmd.merge.tool.args property is split on spaces *then* the tokens
# are replaced by actual files names. This avoids the need to quote arguments to
# handle spaces in paths.
#
# NOTE: Some merge tools (like SmartSynchronize) do not accept the output file
# separately so there is no way to know if the merge was completed. In this case,
# the base file is where the result is written so Cmd just copies the content of
# that file back as the result.
#
# You can add the appropriate lines to customize your Cmd configuration. See
# below for details.

# The arguments for p4merge, see below for download:
# http://www.perforce.com/product/components/perforce-visual-merge-and-diff-tools
cmd.merge.tool.args.p4merge={base} {user} {generated} {out}

# SourceGear (http://www.sourcegear.com/diffmerge/index.html)
cmd.merge.tool.args.sourcegear=--merge --result={out} {user} {base} {generated}

# kdiff3 (http://sourceforge.net/projects/kdiff3/files/kdiff3/)
cmd.merge.tool.args.kdiff3={base} {user} {generated} -o {out}

# Syntevo SmartSynchronize 3 (http://www.syntevo.com/smartsynchronize/index.html).
cmd.merge.tool.args.smartsync={user} {generated} {base}

# TortoiseMerge (part of TortoiseSVN - see http://tortoisesvn.net).
cmd.merge.tool.args.tortoise=-base:{base} -theirs:{generated} -mine:{user} -merged:{out}

# AraxisMerge (see http://www.araxis.com/merge-overview.html):
cmd.merge.tool.args.araxis=-wait -merge -3 -a1 {base} {user} {generated} {out}

# The address where Sencha Inspector is located
inspector.address=http://localhost:1839/

# this variable references a json file containing unicode code points to be
# printed in escaped form during code generation.
cmd.unicode.escapes=${cmd.dir}/unicode-escapes.json

#------------------------------------------------------------------------------
# Customizing Configuration
#
# Customization can be handled any of these ways:
#
#   1. Place customizations in this file (ideally at the bottom) and they will
#      configure this instance of Sencha Cmd.
#
#   2. Create a "sencha.cfg" file in the folder above this instance of Sencha Cmd
#      to be shared by all installed versions.
#
#   3. Create a "~/.sencha/cmd/sencha.cfg" file. On Windows, the "~" maps to your
#      %HOMEDRIVE%%HOMEPATH% folder (e.g., "C:\Users\<USER>