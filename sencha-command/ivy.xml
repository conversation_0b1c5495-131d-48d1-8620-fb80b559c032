<ivy-module version="2.0"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:noNamespaceSchemaLocation=
                    "http://www.jayasoft.org/misc/ivy/samples/ivy.xsd"
            xmlns:m="http://ant.apache.org/ivy/maven">

    <info organisation="com.sencha" module="sencha-command"/>

    <configurations>
        <conf name="compile" description="Required to compile application"/>
        <conf name="runtime" description="Additional run-time dependencies" extends="compile"/>
        <conf name="test"    description="Required for test only" extends="runtime"/>
    </configurations>

    <dependencies>
        <dependency org="org.mozilla" name="rhino" rev="1.7R4" conf="runtime->default"/>
        <dependency org="org.apache.ant" name="ant" rev="1.8.4" conf="runtime->default"/>
        <dependency org="org.slf4j" name="slf4j-api" rev="1.6.6" conf="compile->default"/>
        <dependency org="org.slf4j" name="slf4j-jdk14" rev="1.6.6" conf="runtime->default"/>
        <dependency org="com.google.code.gson" name="gson" rev="2.11.0" conf="runtime->default"/>
        <dependency org="org.apache.velocity" name="velocity-engine-core" rev="2.4.1" conf="runtime->default">
            <exclude org="commons-collections" module="commons-collections"/>
        </dependency>
        <dependency org="com.sencha.tools.external" name="yuicompressor" rev="2.4.7" conf="runtime->default"/>
        <dependency org="ant-contrib" name="ant-contrib" rev="1.0b3" conf="runtime->default">
            <exclude name="ant"/>
        </dependency>
        <dependency org="commons-io" name="commons-io" rev="2.18.0" conf="runtime->default"/>
        <dependency org="org.fusesource.jansi" name="jansi" rev="1.9" conf="runtime->default"/>
        <!-- <dependency org="com.google.javascript" name="closure-compiler" rev="v20180610" conf="runtime->default"/> -->
        <dependency org="com.google.javascript" name="closure-compiler" rev="v20220301" conf="runtime->default"/>
        <dependency org="com.google.javascript" name="closure-compiler-externs" rev="v20220301" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty" name="jetty-server" rev="8.1.22.v20160922" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty" name="jetty-webapp" rev="8.1.22.v20160922" conf="runtime->default"/>
        <dependency org="org.eclipse.jetty" name="jetty-servlets" rev="8.1.22.v20160922" conf="runtime->default"/>
        <dependency org="com.phloc" name="phloc-css" rev="3.7.5" conf="runtime->default"/>
        <dependency org="net.java.dev.jna" name="jna" rev="5.12.1" conf="runtime->default"/>
        <dependency org="org.apache.httpcomponents.client5" name="httpclient5" rev="5.4.1" conf="runtime->default">
            <exclude org="commons-collections" module="commons-collections"/>
        </dependency>

        <dependency org="org.bouncycastle" name="bcprov-jdk15on" rev="1.56" conf="runtime->default"/>
        <dependency org="javax.xml.bind" name="jaxb-api" rev="2.3.0" conf="runtime->default"/>
        <dependency org="com.sencha.licenses" name="update" rev="1.1.0" conf="compile->default">
            <artifact name="update" ext="jar"/>
        </dependency>
        <dependency org="org.apache.commons" name="commons-collections4" rev="4.5.0-M2" force="true"/>
        <dependency org="org.graalvm.js" name="js" rev="21.2.0"/>
        <dependency org="org.graalvm.js" name="js-scriptengine" rev="21.2.0"/>
    </dependencies>
    
</ivy-module>
