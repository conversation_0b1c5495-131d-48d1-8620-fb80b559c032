/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.watchers;

import com.sencha.exceptions.BasicException;
import com.sencha.exceptions.ExFile;
import com.sencha.exceptions.ExNotFound;
import com.sencha.jna.bsd.BSDLibc;
import com.sencha.logging.SenchaLogManager;
import com.sencha.util.BlockingBuffer;
import com.sencha.util.Pair;
import com.sencha.util.PathUtil;
import com.sencha.util.StringUtil;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import org.apache.commons.lang3.NotImplementedException;
import org.slf4j.Logger;

import java.io.File;
import java.io.IOException;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.sencha.jna.bsd.BSDLibc.*;
import static java.nio.file.StandardWatchEventKinds.ENTRY_DELETE;
import static java.nio.file.StandardWatchEventKinds.ENTRY_MODIFY;

public class KQueueWatchService implements WatchService {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private int _kqueueHandle;
    private volatile boolean _closed = false;

    private BlockingBuffer<KQueueWatchKey> _pendingKeys = new BlockingBuffer<KQueueWatchKey>();

    private Map<String, KQueueWatchKey> _watchKeys = new HashMap<String, KQueueWatchKey>();
    private Map<Long, KQueueWatchKey> _keysByFd = new HashMap<Long, KQueueWatchKey>();

    private KQueueWatchKey _interruptKey;

    int _loopInterruptFd;
    File _loopInterruptFile;

    private volatile boolean _polling;

    public KQueueWatchService() {
        _logger.debug("creating KQueue WatchService adapter");
        try {
            _kqueueHandle = BSDLibc.INSTANCE.kqueue();
            if(_kqueueHandle == -1) {
                String message = StringUtil.formatTemplate("could not create kqueue : {0} (1)",
                    Native.getLastError(),
                    BSDLibc.INSTANCE.strerror(Native.getLastError()));
                throw new BasicException(message);
            }
            _loopInterruptFile = File.createTempFile("__kqueue_interrupt_control", ".sync");
            _loopInterruptFile.createNewFile();
            _loopInterruptFile.deleteOnExit();

            _interruptKey = (KQueueWatchKey) register(_loopInterruptFile, new WatchEvent.Kind[]{
                ENTRY_MODIFY
            });
        } catch (Exception ex) {
            throw BasicException.raise(ex);
        }
    }

    @Override
    public void close() throws IOException {
        if(!_closed) {
            _closed = true;
            interruptKernelPoll();
            for(KQueueWatchKey key : _watchKeys.values()) {
                key.close();
            }
            _watchKeys.clear();
            _keysByFd.clear();
            BSDLibc.INSTANCE.close(_kqueueHandle);
            _loopInterruptFile.delete();
        }
    }

    @Override
    public WatchKey poll() {
        throw new NotImplementedException();
    }

    @Override
    public WatchKey poll(long timeout, TimeUnit unit) throws InterruptedException {
        throw new NotImplementedException();
    }

    private void interruptKernelPoll() {
        _logger.debug("Interrupting kernel poll");
        _loopInterruptFile.setLastModified(System.currentTimeMillis());
    }

    public WatchKey register(String path, WatchEvent.Kind<?>[] events, WatchEvent.Modifier... modifiers) {
        return register(Paths.get(PathUtil.getCanonicalPath(path)), events, modifiers);
    }

    public WatchKey register(File path, WatchEvent.Kind<?>[] events, WatchEvent.Modifier... modifiers) {
        return register(Paths.get(PathUtil.getCanonicalPath(path)), events, modifiers);
    }

    public WatchKey register(Path path, WatchEvent.Kind<?>[] events, WatchEvent.Modifier... modifiers) {
        _logger.debug("Adding pending kqueue watch path {}", path.toString());
        KQueueWatchKey key = _watchKeys.get(path.toString());
        if(key == null) {
            key = new KQueueWatchKey(path);
            _watchKeys.put(path.toString(), key);
        }
        key.addEventKinds(events);
        _pendingKeys.add(key);
        interruptKernelPoll();
        return key;
    }

    public void registerPendingFiles() {
        kevent64 modificationEvent = new kevent64();
        modificationEvent.write();
        Pointer modificationEventPtr = modificationEvent.getPointer();
        for(KQueueWatchKey key : _pendingKeys.drainAvailable()) {
            _logger.debug("Registering kqueue watch path {}", key.watchable().toString());
            modificationEvent.set(key.createKEvent());
            modificationEvent.write();
            int ret = BSDLibc.INSTANCE.kevent64(
                _kqueueHandle,
                modificationEventPtr,
                1,
                Pointer.NULL,
                0,
                0,
                Pointer.NULL);

            if(ret != 0) {
                int error = ret;
                if(ret == -1) {
                    error = Native.getLastError();
                }
                String message = StringUtil.formatTemplate("kevent modification failure code \"{1} : {2}\" for \"{0}\"",
                    key.watchable().toString(),
                    error,
                    BSDLibc.INSTANCE.strerror(error));
                _logger.error(message);
            }
            _keysByFd.put(key.getFileDescriptor(), key);
        }
    }

    private Pair<Integer, kevent64> pollKEvent(kevent64 event) {
        _logger.debug("beginning event poll now...");
        Pointer eventPtr = event.getPointer();
        int code;
        _polling = true;
        _logger.debug("polling kqueue ...");
        code = BSDLibc.INSTANCE.kevent64(
            _kqueueHandle,
            Pointer.NULL,
            0,
            eventPtr,
            1,
            0,
            Pointer.NULL);
        _polling = false;
        event.read();
        return new Pair(code, event);
    }

    @Override
    public WatchKey take() throws InterruptedException {
        _logger.debug("take called!!, closed is {}", _closed);
        kevent64 pollevent = new kevent64();
        while(!_closed) {
            registerPendingFiles();
            Pair<Integer, kevent64> result = pollKEvent(pollevent);
            int code = result.getLeft();
            kevent64 event = result.getRight();
            if(code < 0) {
                int errcode = Native.getLastError();
                String message = StringUtil.formatTemplate(
                    "error polling kqueue : {0} {1}",
                    errcode,
                    BSDLibc.INSTANCE.strerror(errcode));
                if(_logger.isDebugEnabled()) {
                    _logger.error(message);
                }
                // err 4 is system interrupted
                if(errcode != 4) {
                    throw new BasicException(message).raise();
                }
                continue;
            } else if(code > 0) {
                long fd = event.ident;
                KQueueWatchKey key = _keysByFd.get(fd);
                if(key != null) {
                    _logger.debug("Get kqueue event for {}", key.watchable().toString());
                    if (_interruptKey.getFileDescriptor() == event.ident) {
                        continue;
                    }

                    int flags = event.fflags;
                    if (BSDLibc.EventTypeFlags.NOTE_DELETE.isSet(flags) ||
                        BSDLibc.EventTypeFlags.NOTE_RENAME.isSet(flags)) {
                        remove(key);
                    }

                    key.load(event);
                    return key;
                } else {
                    _logger.debug("Failed to lookup watch key for event id {}", event.ident);
                }
            }
        }
        return null;
    }

    protected void remove(KQueueWatchKey key) {
        _logger.debug("Removing kqueue watch path {}", key.watchable().toString());
        _watchKeys.remove(key.watchable().toString());
        _keysByFd.remove(key.getFileDescriptor());
        key.close();
    }


    @Override
    public void finalize() {
        try {
            close();
        } catch (Exception ex) {
            if(_logger.isDebugEnabled()) {
                _logger.error(BasicException.stringify(ex));
            }
        } finally {

        }
    }

    public static class KQueueWatchKey implements WatchKey {

        private Path _watchable;
        private BlockingBuffer<WatchEvent<?>> _events = new BlockingBuffer<WatchEvent<?>>();
        private Set<WatchEvent.Kind<?>> _registeredEventKinds = new HashSet<WatchEvent.Kind<?>>();

        private Long _fileDescriptor;

        public KQueueWatchKey(Path watchable) {
            _watchable = watchable;
        }

        public void addEventKinds(WatchEvent.Kind<?>... kinds) {
            Collections.addAll(_registeredEventKinds, kinds);
        }

        @Override
        public boolean isValid() {
            return _fileDescriptor != null;
        }

        @Override
        public List<WatchEvent<?>> pollEvents() {
            return new ArrayList<WatchEvent<?>>( _events.drainAvailable());
        }

        @Override
        public boolean reset() {
            return isValid();
        }

        @Override
        public void cancel() {

        }

        @Override
        public Watchable watchable() {
            return _watchable;
        }

        public void load(kevent event) {
            _events.add(new KQueueWatchEvent(_watchable, event));
        }

        public void load(kevent64 event) {
            _events.add(new KQueueWatchEvent(_watchable, event));
        }

        private static int getEventMask(WatchEvent.Kind<?>... kinds) {
            int flags = 0;
            for(WatchEvent.Kind<?> kind : kinds) {
                if(kind == ENTRY_MODIFY) {
                    flags |= (
                        BSDLibc.EventTypeFlags.NOTE_ATTRIB.getValue() |
                            BSDLibc.EventTypeFlags.NOTE_WRITE.getValue() |
                            BSDLibc.EventTypeFlags.NOTE_REVOKE.getValue() |
                            BSDLibc.EventTypeFlags.NOTE_LINK.getValue() |
                            BSDLibc.EventTypeFlags.NOTE_EXTEND.getValue());
                }
                if(kind == ENTRY_DELETE) {
                    flags |= (
                        BSDLibc.EventTypeFlags.NOTE_DELETE.getValue() |
                            BSDLibc.EventTypeFlags.NOTE_RENAME.getValue());
                }
            }
            return flags;
        }

        public long getFileDescriptor() {
            if(_fileDescriptor == null) {
                int fd = BSDLibc.INSTANCE.open(
                    _watchable.toString(),
                    BSDLibc.FOpenFlags.O_EVTONLY.getValue(),
                    0);
                if(fd == 0) {
                    throw new ExNotFound("File not found : {0}", _watchable.toString());
                } else if(fd == -1) {
                    int error = Native.getLastError();
                    String message = StringUtil.formatTemplate("kevent modification failure code \"{1} : {2}\" for \"{0}\"",
                        watchable().toString(),
                        error,
                        BSDLibc.INSTANCE.strerror(error));
                    _logger.error(message);
                    throw new ExFile(message);
                }
                _fileDescriptor = new Long(fd);
            }
            return _fileDescriptor.intValue();
        }

        public kevent64 createKEvent() {
            kevent64 event = new kevent64();
            event.ident = getFileDescriptor();
            event.filter = BSDLibc.EVFILT_VNODE;
            event.flags = (short)(
                BSDLibc.EventFlags.EV_ADD.getValue() |
                BSDLibc.EventFlags.EV_CLEAR.getValue());
            event.fflags = getEventMask(_registeredEventKinds.toArray(new WatchEvent.Kind<?>[_registeredEventKinds.size()]));
            event.data = 0;
            event.udata = 0;
            return event;
        }

        public void close() {
            if(_fileDescriptor != null) {
                _logger.debug("Closing file handle for {}", _watchable.toString());
                // This will automatically remove the file from the kqueue :
                BSDLibc.INSTANCE.close(_fileDescriptor.intValue());
                _fileDescriptor = null;
            }
        }

        @Override
        public void finalize() {
            close();
        }
    }

    public static class KQueueWatchEvent implements WatchEvent<Path> {

        private Path _context;
        private Kind<Path> _kind;

        public KQueueWatchEvent(Path path, kevent event) {
            _context = path;

            int fflags = event.fflags;
            if(BSDLibc.EventTypeFlags.NOTE_DELETE.isSet(fflags) ||
               BSDLibc.EventTypeFlags.NOTE_RENAME.isSet(fflags)) {
                _kind = ENTRY_DELETE;
            } else {
                _kind = ENTRY_MODIFY;
            }
        }

        public KQueueWatchEvent(Path path, kevent64 event) {
            _context = path;

            int fflags = event.fflags;
            if(BSDLibc.EventTypeFlags.NOTE_DELETE.isSet(fflags) ||
                BSDLibc.EventTypeFlags.NOTE_RENAME.isSet(fflags)) {
                _kind = ENTRY_DELETE;
            } else {
                _kind = ENTRY_MODIFY;
            }
        }

        @Override
        public Kind<Path> kind() {
            return _kind;
        }

        @Override
        public int count() {
            return 1;
        }

        @Override
        public Path context() {
            return _context;
        }
    }
}
