/*
 * Copyright (c) 2012-2023. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

import jdk.nashorn.internal.codegen.types.Type;

public class ForAwaitOfStatement extends BaseNode {

    private BaseNode _initializer;
    private BaseNode _collection;
    private BaseNode _body;

    @Override
    <T> void doVisit(NodeVisitor<T> vis) {
     vis.onForAwaitOfStatement(this);
    }

    @Override
    public <T> void descend(NodeVisitor<T> vis) {
     vis.visit(_initializer);
     vis.visit(_collection);
     vis.visit(_body);
    }

    public BaseNode getInitializer() {
        return _initializer;
    }

    public void setInitializer(BaseNode initializer) {
        this._initializer = initializer;
    }

    public BaseNode getCollection() {
        return _collection;
    }

    public void setCollection(BaseNode collection) {
        this._collection = collection;
    }

    public BaseNode getBody() {
        return _body;
    }

    public void setBody(BaseNode body) {
        this._body = body;
    }

}
