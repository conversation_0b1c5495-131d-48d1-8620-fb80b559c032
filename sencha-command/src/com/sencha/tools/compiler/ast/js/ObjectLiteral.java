/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

public class ObjectLiteral extends NodeContainer<ObjectProperty> {

    @Override
    <T> void doVisit(NodeVisitor<T> vis) {
        vis.onObjectLiteral(this);
    }
    
    private void addCustomProp (BaseNode name, BaseNode val) {
        ObjectProperty prop = new ObjectProperty();
        prop.setName(name);
        prop.setValue(val);
        prop.setOptimized(val);
        addElement(prop);
    }
    
    public void addProperty (BaseNode el) {
        if (el instanceof FunctionNode) {
            FunctionNode func = (FunctionNode)el;
            addCustomProp(func.getName(), func);
        }
        else if (el instanceof GetAccessor) {
            GetAccessor acc = (GetAccessor)el;
            addCustomProp(acc.getName(), acc);
        }
        else if (el instanceof SetAccessor) {
            SetAccessor acc = (SetAccessor) el;
            addCustomProp(acc.getName(), acc);
        }
        else if (el instanceof SpreadExpression) {
            SpreadExpression spe = (SpreadExpression) el;
            addCustomProp(spe.getOperand(), spe);
        }
        else if (el instanceof ObjectSpread) {
            ObjectSpread objspe = (ObjectSpread) el;
            addCustomProp(objspe.getOperand(), objspe);
        }
        else if (el instanceof OptionalMemberExpression)
        {
            OptionalMemberExpression optionalMemberExpression = (OptionalMemberExpression) el;
            addCustomProp(optionalMemberExpression.getLeft(), optionalMemberExpression.getRight());
        }
        else if (el instanceof RestParameter) {
            RestParameter rest = (RestParameter) el;
            addCustomProp(rest.getOperand(), rest);
        }
        else if (el instanceof DefaultParameter) {
            DefaultParameter defaultParam = (DefaultParameter) el;
            addCustomProp(defaultParam.getName(), defaultParam.getDefaultValue());
        }
        else if (el instanceof PassthroughNode) {
            // Handle PassthroughNode by preserving it as a custom property
            PassthroughNode passthroughNode = (PassthroughNode) el;
            addCustomProp(passthroughNode, passthroughNode);
        }
        else {
            addElement((ObjectProperty) el);
        }
    }
}
