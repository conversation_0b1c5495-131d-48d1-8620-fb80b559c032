/*
 * Copyright (c) 2012. Sencha Inc.
 */
package com.sencha.tools.compiler.jsb.statements;

import com.sencha.exceptions.BasicException;
import com.sencha.logging.SenchaLogManager;
import com.sencha.util.CharsetDetector;
import com.sencha.util.Converter;
import com.sencha.util.ExpressionUtil;
import com.sencha.util.FileUtil;
import com.sencha.util.RegexUtil;

import javax.script.*;
import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;

import static com.sencha.util.StringUtil.formatString;
import java.nio.charset.Charset;

public class Parser {

    private static final Logger _logger = SenchaLogManager.getLogger();

    private Map<String, Object> _params = new HashMap<String, Object>();

    private boolean _includeDirectiveTags = Statement.IncludeDirectiveTags;
    
    public Parser setIncludeDirectiveTags(boolean enable) {
        _includeDirectiveTags = enable;
        return this;
    }
    
    public boolean getIncludeDirectiveTags() {
        return _includeDirectiveTags;
    }
    
    public String parse (File f) {
        if(_logger.isTraceEnabled()) {
            _logger.trace("parsing file");
        }
        return parse(FileUtil.readFileData(f));
    }

    public String parse (byte[] data) {
        return parse(CharsetDetector.decodeBytes(data));
    }
    
    public String parse (byte[] data, Charset charset) {
        return parse(new String(data, charset));
    }
    
    public String parse (String data) {
        try {
            if(_logger.isTraceEnabled()) {
                _logger.trace("beginning jsb statement parse");
            }

            // need to default this in
            // TODO: resolve why this variable is always needed in the jsbparsecontext data
            // to avoid exceptions (track down the file)
            if(!_params.containsKey("classSystem")) {
                _params.put("classSystem", true);
            }

            BufferedReader reader = 
                    new LineNumberReader(new StringReader(data));
            Statement stmt = new Statement(this);
            try {
                String parsedData = stmt.parse(reader);
                return parsedData;
            } finally {
                if(_logger.isTraceEnabled()) {
                    _logger.trace("jsb statement parse complete");
                }
                reader.close();
            }
        } catch (Exception e) {
            throw new BasicException(e).raise();
        }
    }

    private static final Map<String, String> _modifierMap = new HashMap<String, String>();

    static {
        _modifierMap.put("!==", "!==");
        _modifierMap.put("!=", "!==");
        _modifierMap.put("!", "!==");
        _modifierMap.put("=", "===");
    }

    public boolean evaluate(String name) {
        return evaluate(name, null);
    }

    public boolean evaluate (String name, Object value) {
        String modifier = "===";
        final Pattern modPat = RegexUtil.getInstance().get("^(!=*|<=|>=|<|>|=+)");

        if (value == null) {
            value = true;
        }

        if (value instanceof String) {
            String str = (String) value;
            Matcher match = modPat.matcher(str);

            if (match.find()) {
                modifier = match.group(0);
                value = str.substring(modifier.length());
            }

            if ("true".equals(str)) {
                value = true;
            } else if ("false".equals(str)) {
                value = false;
            } else {
                try {
                    if (!(new Double(str).isNaN())) {
                        value = new Double(str);
                    }
                } catch (Exception e) {
                    value = formatString("'%s'", value);
                }
            }

        }

        // clean up the operator
        if(_modifierMap.containsKey(modifier)) {
            modifier = _modifierMap.get(modifier);
        }

        // construct js expression and evaluate
        Bindings bindings = new SimpleBindings(_params);
        if(!bindings.containsKey(name)) {
            bindings.put(name, false);
        }

        Object val = bindings.get(name);
        if(val instanceof String ){
            String valStr = (String) val;
            Object[] output = new Object[1];
            if(value instanceof Boolean){
                if (Converter.tryConvert(valStr,  Boolean.class, output)) {
                    if(_logger.isTraceEnabled()) {
                        _logger.trace("Converted string to boolean");
                    }
                    bindings.put(name, output[0]);
                }
            } else if(value instanceof Number) {
                if (Converter.tryConvert(valStr,  Double.class, output)) {
                    if(_logger.isTraceEnabled()) {
                        _logger.trace("Converted string to double");
                    }
                    bindings.put(name, output[0]);
                }
            }
        }

        if(_logger.isTraceEnabled()) {
            Object obj = bindings.get(name);
            _logger.trace("value : '{}' : '{}'",
                value,
                value != null ? value.getClass().getCanonicalName() : null);

            _logger.trace("context : '{}' : '{}'",
                obj,
                obj != null ? obj.getClass().getCanonicalName() : null);
        }

        String expr = formatString("%s %s %s", name, modifier, value);
        return (Boolean) ExpressionUtil.getInstance().eval(expr, bindings);
    }

    public Parser setParams (Map<String, Object> params) {
        _params = (params != null)
                  ? params
                  : new HashMap<String, Object>();

        return this;
    }

    public Parser setParam(String name, Object val) {
        _params.put(name, val);
        return this;
    }

    public boolean isCloseOf (String line, Statement statement) {
        return isCloseOf(line, statement.getData());
    }

    public boolean isCloseOf(String line, Statement.Data statement) {
        if (statement.type == null) {
            return false;
        }

        line = line.trim();
        String type = ((statement.isInverted)
                        ? "!"
                        : "") +
                       statement.type,
                rx = "^//(?:\\t|\\s)*</" + type + "(\\s+.*)?>$";

        Pattern pat = RegexUtil.getInstance().get(rx);
        return pat.matcher(line).find();
    }

    public boolean isStatement (String line) {
        return parseStatementParts(line) != null;
    }

    public Matcher parseStatementParts (String line) {
        Matcher m = RegexUtil.getInstance().get("^//(?:\\t|\\s)*<([^/]+)>$").matcher(line.trim());
        if (m.find()) {
            return m;
        }
        return null;
    }

    private String expect (Pattern p, StringBuilder target) {
        Matcher m = p.matcher(target.toString());
        String result, tmp;
        if (m.find()) {
            result = target.substring(m.start(), m.end());
            if(m.group(0).length() < target.length()) {
                tmp = target.substring(m.group(0).length());
                target.setLength(0);
                target.append(tmp);
            } else {
                target.setLength(0);
            }
            return result;
        }
        return null;
    }

    private Pattern _front = RegexUtil.getInstance().get("^[^\\w]+", Pattern.CASE_INSENSITIVE);
    private Pattern _nameRx = RegexUtil.getInstance().get("^[\\w\\.\\-_]+", Pattern.CASE_INSENSITIVE);
    private Pattern _equalsSignRx = RegexUtil.getInstance().get("^=");
    private Pattern _valueWrapperRx = RegexUtil.getInstance().get("^('|\")", Pattern.CASE_INSENSITIVE);

    public Map<String, Object> parseStatementProperties (String line) {

        Map<String, Object> props = new HashMap<String, Object>();
        StringBuilder sb = new StringBuilder().append(line);

        String name, equalSign, valueWrapper, valueCheck, value;

        while (sb.length() > 0) {
            expect(_front, sb);
            name = expect(_nameRx, sb);

            if (name == null) {
                break;
            }

            equalSign = expect(_equalsSignRx, sb);

            if (equalSign == null) {
                props.put(name, true);
                continue;
            }

            valueWrapper = expect(_valueWrapperRx, sb);
            valueCheck = (valueWrapper != null)
                         ? valueWrapper
                         : "\\s";

            value = expect(RegexUtil.getInstance().get("^[^" + valueCheck + "]+"), sb);

            if (valueWrapper != null) {
                expect(RegexUtil.getInstance().get(valueWrapper), sb);
            }

            props.put(name, value);
        }

        return props;
    }


    private Pattern _typeMatchRx = RegexUtil.getInstance()
        .get("^(!)?([\\w]+)", Pattern.CASE_INSENSITIVE);

    public Statement.Data parseStatement (String line) {
        String str = line.trim();
        Matcher parts = parseStatementParts(str), typeMatch;
        Statement.Data statement = null;

        if (parts == null) {
            return null;
        }

        str = parts.group(1);
        typeMatch = _typeMatchRx.matcher(str);

        if (!typeMatch.find()) {
            return null;
        }

        statement = new Statement.Data();

        statement.type = typeMatch.group(2);
        statement.isInverted = ("!".equals(typeMatch.group(1)));

        str = str.substring(typeMatch.group(0).length()).trim();
        statement.properties = parseStatementProperties(str);
        statement.parser = this;

        return statement;
    }

}
