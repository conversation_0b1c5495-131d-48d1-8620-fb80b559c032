package com.sencha.command.compile.app;

import com.sencha.cli.annotations.Doc;
import com.sencha.cli.annotations.Private;
import com.sencha.cli.annotations.Required;
import com.sencha.command.compile.CompileCommands;
import com.sencha.command.environment.BuildEnvironment;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.app.AppManifest;
import com.sencha.tools.inspector.InspectorConnector;
import com.sencha.tools.page.AppJsonBuilder;
import com.sencha.tools.page.PageModel;
import com.sencha.tools.page.PageModelBuilder;
import com.sencha.util.*;
import org.slf4j.Logger;

import java.io.File;
import java.util.*;

public class MicroloadCommand extends BaseAppOutputCommand {
    private static final Logger _logger = SenchaLogManager.getLogger();
    
    private boolean _bootstrap = false;
    private boolean _slicerPage = false;
    private Operation _operation = Operation.Json;
    private boolean _preprocess = true;
    private boolean _fashion = false;
    private String _resourcePath;
    private List<String> _manifestExcludes = new ArrayList<String>();
    private boolean _loadAllJsResources = false;
    private boolean _excludeBundle = false;
    private String _jsonp = null;

    public MicroloadCommand(CompileCommands commands) {
        super(commands);
    }

    @Doc("enables / disables bootstrap json generation")
    public void setBootstrap(boolean enable) {
        _bootstrap = enable;
    }

    public boolean getBootstrap() {
        return _bootstrap;
    }

    public boolean getSlicerPage() {
        return _slicerPage;
    }

    @Doc("Enables generation of a slicer page's bootstrap.json content")
    public void setSlicerPage(boolean slicerPage) {
        _slicerPage = slicerPage;
    }

    @Doc("the path to the microloader to embed")
    public void setMicroloaderPath(String path) {
        getAppJsonBuilder().setMicroloaderPath(path);
    }
    
    @Doc("the path to the boot loader file")
    public void setBootPath(String path) {
        getAppJsonBuilder().setBootPath(path);
    }
    
    @Doc("Set the resource load behavior of the microloader")
    public void setMode(PageModel.MicroloadMode mode) {
        getAppJsonBuilder().setMicroloaderMode(mode);
    }
    
    @Required
    @Doc("Sets the microload operation to perform")
    public void setOperation(Operation op) {
        _operation = op;
    }
    
    public Operation getOperation() {
        return _operation;
    }
    
    public void setResourcePath(String resourcePath) {
        _resourcePath = resourcePath;
    }
    
    public String getResourcePath() {
        return _resourcePath;
    }

    public boolean getPreprocess() {
        return _preprocess;
    }

    @Doc("Enables / disables preprocessing microloader content. (default: enabled)")
    public void setPreprocess(boolean preprocess) {
        _preprocess = preprocess;
    }

    @Doc("Excludes root properties from the generated manifest json object. (command separated list)")
    public void setExclude(String excludes) {
        Collections.addAll(_manifestExcludes, StringUtil.split(excludes, ","));
    }
    
    @Private
    @Doc("Include fashion tags on manifest")
    public void setFashion (boolean enable) {
        _fashion = enable;
    }

    public boolean getLoadAllJsResources() {
        return _loadAllJsResources;
    }

    @Private
    @Doc("Replaces the js array with a generated list for all loadOrder resources.")
    public void setLoadAllJsResources(boolean loadAllJsResources) {
        _loadAllJsResources = loadAllJsResources;
    }


    public boolean getExcludeBundle() {
        return _excludeBundle;
    }

    @Private
    @Doc("When loading all js resources, this option will exclude bundle js resources.")
    public void setExcludeBundle(boolean excludeBundle) {
        _excludeBundle = excludeBundle;
    }

    public void execute() {
        AppJsonBuilder appJsonBuilder = getAppJsonBuilder();
        BuildEnvironment be = appJsonBuilder.getBuildEnvironment();
        Operation op = getOperation();
        
        String content = null;
        PageModel outModel;
        
        switch(op) {
            case Json:
                outModel = getOutputPageModel();
                content = outModel.toJson();
                break;
            case Manifest:
                outModel = getOutputPageModel();
                AppManifest manifest = getAppManifest(be);
                
                if(!StringUtil.isNullOrEmpty(_resourcePath) && !StringUtil.isNullOrEmpty(_basePath)) {
                    outModel.adjustRelativePaths(_basePath, _resourcePath);
                }
                
                if (getBootstrap() && InspectorConnector.isEnabled()) {
                    InspectorConnector.getInstance().prepareManifest(outModel);
                }
                
                Map<String, Object> outMap =
                    buildOutputManifest(outModel, manifest, be);
                    
                if (_fashion) {
                    List<String> tags = (List<String>) outMap.get("tags");
                    if (tags == null) {
                        outMap.put("tags", tags = new ArrayList<String>());
                    }
                    if (!tags.contains("fashion")) {
                        tags.add("fashion");
                    }
                }
                
                String json = JsonUtil.toJson(outMap);
                String hash = StringUtil.createChecksum(json);
                outMap.put("hash", hash);

                Map<String, String> resPaths = getBuildEnvironment().getResourceOutputPaths();
                if (resPaths != null) {
                    for (String key : resPaths.keySet()) {
                        String value = resPaths.get(key);
                        String actual = PathUtil.getCanonicalPath(value);
                        String relative = PathUtil.getRelativePath(_basePath, actual);
                        relative = PathUtil.convertPathCharsToUnix(relative);
                        if (relative.endsWith("/")) {
                            relative = relative.substring(0, relative.length() - 1);
                        }
                        if (relative.isEmpty()) {
                            relative = ".";
                        }
                        resPaths.put(key, relative);
                    }
                    outMap.put("resources", resPaths);
                }

                if (getLoadAllJsResources()) {
                    addAllJsResources(outMap);
                }

                content = JsonUtil.toJson(outMap, false);
                String jsonpFunc = getJsonp();
                if (!StringUtil.isNullOrEmpty(jsonpFunc)) {
                    String outputPath = getOutputPath() + "p";
                    FileUtil.writeFile(outputPath, jsonpFunc + '(' + content + ");");
                }
                break;
            case Microloader:
                content = FileUtil.readFile(appJsonBuilder.getMicroloaderPath());
                if(!StringUtil.isNullOrEmpty(appJsonBuilder.getBootPath())) {
                    content = StringUtil.join(new String[]{
                        FileUtil.readFile(appJsonBuilder.getBootPath()),
                        content
                    }, StringUtil.NewLine);
                }
                if(_preprocess) {
                    content = getCompileCommands().getContext()
                        .getPreprocessor()
                        .preprocessJsSource(content);
                }
                break;
            default:
                break;
        }
        
        writeContent(content);
    }

    private void addAllJsResources(Map outMap) {
        Object loadOrder = outMap.get("loadOrder");
        if (loadOrder != null) {
            List loadOrderList = (List)loadOrder;
            List<Map> newJsArray = new ArrayList<Map>();
            List<Map> loadOrderJsArray = new ArrayList<Map>();
            List currJsArray = (List) outMap.get("js");
            boolean jsFound = false;
            int insertIndex = -1;
            int idx = 0;
            Set<String> bundle = new LinkedHashSet<String>();

            for(Object loItem : loadOrderList) {
                Map item = (Map)loItem;
                Map newItem = new LinkedHashMap();
                newItem.put("path", item.get("path"));
                loadOrderJsArray.add(newItem);
            }

            if (currJsArray != null && currJsArray.size() > 0) {
                for (Object elem : currJsArray) {
                    Map elemMap = (Map)elem;
                    if (elemMap.get("bundle") == null && !_excludeBundle) {
                        newJsArray.add(elemMap);
                    }
                    else {
                        if (!jsFound) {
                            jsFound = true;
                            insertIndex = idx;
                        }
                        String elemPath = (String) elemMap.get("path");
                        String fullPath = PathUtil.getCanonicalPath(new File(_basePath, elemPath));
                        bundle.add(fullPath);
                    }
                    idx++;
                }


                if (insertIndex > -1) {
                    for (Map m : loadOrderJsArray) {
                        String path = (String) m.get("path");
                        String fullPath = PathUtil.getCanonicalPath(new File(_basePath, path));
                        if (PathUtil.exists(fullPath)) {
                            if (!bundle.contains(fullPath) || !_excludeBundle) {
                                newJsArray.add(insertIndex, m);
                                insertIndex++;
                            }
                        }
                    }
                }
                else {
                    newJsArray.addAll(loadOrderJsArray);
                }

            }
            else {
                newJsArray = loadOrderJsArray;
            }

            outMap.put("js", newJsArray);
            outMap.remove("loadOrder");
        }
    }


    private Map<String, Object> buildOutputManifest(
        PageModel model,
        AppManifest manifest,
        BuildEnvironment be) 
    {
        Map<String, Object> output =
            AppManifest.produceFullAppManifest(model, manifest, be.mustGetAppOrPackageEnvironment());
        JsonObjectHelper helper = new JsonObjectHelper();

        List<String> filters = new ArrayList<String>();
        Collections.addAll(filters, AppJsonBootstrapExcludes);
        Collections.addAll(filters, PackageJsonExcludes);
        if(!getBootstrap()) {
            Collections.addAll(filters, AppJsonFullExcludes);
        }

        output = helper.exclude(output, filters);
        if(output.containsKey("packages")) {
            Map<String, Object> packages = (Map<String, Object>) output.get("packages");
            // we need to preserve the 'requires' property on the package object values
            filters.remove("requires");
            filters.remove("css");
            filters.remove("js");
            for(Object o : packages.values()) {
                if (o instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>)o;
                    helper.exclude(map, filters);
                }
            }
        }

        for(String key : _manifestExcludes) {
            output.remove(key);
        }

        if(be.hasConfigProperty("build.id")) {
            output.put("profile", be.getConfigProperty("build.id"));
        } else {
            output.put("profile", "");
        }

        if(be.hasConfigProperty("app.output.appCache.enable")) {
            output.put("appCacheEnabled", Boolean.valueOf(be.getConfigProperty("app.output.appCache.enable")));
        }

        return output;
    }

    private PageModel getOutputPageModel() {
        String outDir = StringUtil.defaultString(getBasePath(), getOutputDirectory());
        return getOutputPageModel(outDir);
    }
    
    private PageModel getOutputPageModel(String outputDirectory) {
        AppJsonBuilder appJsonBuilder = getAppJsonBuilder();
        PageModelBuilder modelBuilder = appJsonBuilder.getBuilder();
        PageModel outModel;
        if(getBootstrap()) {
            if(getSlicerPage()) {
                outModel = modelBuilder.buildSlicerPageModel(outputDirectory, getCompileCommands().getContext());
            } else {
                outModel = modelBuilder.buildBootstrapPageModel(outputDirectory, getCompileCommands().getContext());
            }
        } else {
            outModel = modelBuilder.buildOutputPageModel();
        }
        return outModel;        
    }

    public String getJsonp() {
        return _jsonp;
    }

    @Doc("indicates that manifest data should be generated in jsonP format")
    public void setJsonp(String jsonp) {
        _jsonp = jsonp;
    }

    public static enum Operation {
        @Doc("Loads the microloader json manifest for output")
        Json,
        @Doc("Constructs a complete microloader manifest with package and loader metadata for output")
        Manifest,
        @Doc("Loads the specified microloader js code for output")
        Microloader,
        @Doc("Generates the output markup")
        Page
    }

    private static final String[] AppJsonFullExcludes = new String[]{
        "classes",
        "loadOrder",
        "paths"
    };

    private static final String[] AppJsonBootstrapExcludes = new String[]{
        "properties",
        "builds",
        "themes",
        "locales",
        "resources",
        "requires",
        "extras",
        "production",
        "development",
        "testing",
        "classpath",
        "overrides",
        "slicer",
        "outputDir",
        "manifest",
        "sass",
        "cordova",
        "phonegap",
        "output",
        "bootstrap",
        "compressor",
        "classic",
        "modern"
    };
    
    private static final String[] PackageJsonExcludes = new String[]{
        "signatures",
        "locker",
        "seal",
        "architect"
    };
}
