/*
 * Copyright (c) 2012-2015. Sencha Inc.
 */

package com.sencha.util.http;

import com.sencha.exceptions.BasicException;
import com.sencha.logging.SenchaLogManager;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.CloseableHttpResponse;
import org.apache.hc.core5.http.Header;
import org.apache.hc.core5.http.HttpEntity;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.message.BasicHeader;
import org.slf4j.Logger;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON> Roldan (<EMAIL>) on 07/08/15.
 */
public class ProxyResponder implements Responder {
    private static final Logger _logger = SenchaLogManager.getLogger();

    public ProxyResponder(String server) {
        _server = server;
    }

    @Override
    public void attach (Responder parent, String name) {
        _name = name;
        _parent = parent;
    }

    @Override
    public void dispatch(Context context) {
        try {
            context.setOutputFormatter(new TextOutputFormatter());
            HttpServletRequest request = context.getRawRequest();
            HttpServletResponse response = context.getRawResponse();
            String uri = request.getRequestURI().replace("/~inspector/", "");
            
            HttpClient httpclient = HttpClients.createDefault();

            String requestUrl;
            if (specialMappings.containsKey(uri)) {
                _logger.debug(uri + " is a special mapping to : " + specialMappings.get(uri));
                requestUrl = request.getRequestURL().toString().replace(request.getRequestURI(),"") + "/" + specialMappings.get(uri);
            } else {
                requestUrl= _server + uri;
            }
            
            HttpGet getRequest = new HttpGet(requestUrl);
            Enumeration<String> headerNames = request.getHeaderNames();
            String headerName;
            while (headerNames.hasMoreElements()) {
                headerName = headerNames.nextElement();
                if (!headerName.equalsIgnoreCase("host")) {
                    getRequest.addHeader(new BasicHeader(headerName, request.getHeader(headerName)));
                }
            }

            _logger.debug("Executing request: " + getRequest.getMethod() + " " + getRequest.getUri());
            CloseableHttpResponse getResponse = (CloseableHttpResponse) httpclient.execute(getRequest);
            response.setStatus(getResponse.getCode());
            Header[] headerArrayResponse = getResponse.getHeaders();
            for(Header header : headerArrayResponse) {
                response.setHeader(header.getName(), header.getValue());
            }
            HttpEntity entity = getResponse.getEntity();
            InputStream inputStreamProxyResponse = entity.getContent();
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStreamProxyResponse);
            OutputStream outputStreamClientResponse = response.getOutputStream();
            int intNextByte;
            while ( ( intNextByte = bufferedInputStream.read() ) != -1 ) {
                outputStreamClientResponse.write(intNextByte);
            }
        } catch (Exception ex) {
            throw BasicException.raise(ex);
        }
    }
    
    public void addSpecialMapping(String path, String serverPath) {
        specialMappings.put(path, serverPath);
    }

    @Override
    public String getName() {
        return _name;
    }

    @Override
    public Responder getParent() {
        return _parent;
    }

    private String _name = "";
    private Responder _parent;
    private String _server;
    
    private Map<String, String> specialMappings = new HashMap<String, String>();
}
