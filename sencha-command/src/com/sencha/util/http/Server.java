/*
 * Copyright (c) 2013. Sencha Inc.
 */
package com.sencha.util.http;

import com.sencha.exceptions.BasicException;
import com.sencha.logging.SenchaLogManager;
import com.sencha.util.*;
import com.sencha.util.http.ssl.KeyStoreUtil;
import org.eclipse.jetty.server.Handler;
import org.eclipse.jetty.server.handler.ContextHandlerCollection;
import org.eclipse.jetty.server.ssl.SslSelectChannelConnector;
import org.eclipse.jetty.servlet.FilterHolder;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.eclipse.jetty.servlet.listener.IntrospectorCleaner;
import org.eclipse.jetty.servlets.ProxyServlet;
import org.eclipse.jetty.util.component.LifeCycle;
import org.eclipse.jetty.util.resource.Resource;
import org.eclipse.jetty.util.ssl.SslContextFactory;
import org.eclipse.jetty.util.thread.ExecutorThreadPool;
import org.eclipse.jetty.webapp.AbstractConfiguration;
import org.eclipse.jetty.webapp.Configuration;
import org.eclipse.jetty.webapp.WebAppContext;
import org.slf4j.Logger;

import javax.servlet.*;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.BindException;
import java.net.InetSocketAddress;
import java.net.URL;
import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This class creates a basic HTTP server for a a given root responder.
 */
public class Server {
    public static Server create (Responder responder, int port) {
        Server server = new Server(responder, port);
        server.start();
        return server;
    }

    public static void shutdown (int port) {
        try {
            URL url = new URL("http", "localhost", port, "/.stop");
            _logger.info("Contacting {} for shutdown port", url);
            InputStream input = url.openStream();
            String token = StreamUtil.readAllAndClose(input);
            _logger.info("Shutdown port is {}", token);
            StopListener.sendStop(token);
        } catch (Exception ex) {
            BasicException.raise(ex);
        }
    }

    public void stop () {
        try {
            _jetty.stop();
            _stopper.stop(); // poetry: one shall stop what stops / because started to stop / otherwise will stop to start
        } catch (Exception ex) {
            BasicException.raise(ex);
        }
    }

    public void waitForStop () {
        _stopper.waitForStop();
        stop();
    }
    
    private Server (Responder responder, int port) {
        this(port);
        if (responder instanceof Stopper.Member) {
            ((Stopper.Member) responder).join(_stopper);
        }
        mount("/", responder);
    }
    
    public Server(int port) {
        _port = port;
        _jetty = new org.eclipse.jetty.server.Server(port);
        initStopper();
    }

    public Server(InetSocketAddress address) {
        _port = address.getPort();
        _address = address;
        _jetty = new org.eclipse.jetty.server.Server(address);

        // set a custom thread pool to avoid InterruptedException messages generated
        // from jetty threads during shutdown
        _jetty.setThreadPool(new ExecutorThreadPool(ThreadUtil.createThreadPoolService(
                ThreadUtil.MIN_THREADPOOL_SIZE + 2,
                ThreadUtil.MAX_THREAD_POOL_SIZE + 2,
                ThreadUtil.KEEP_ALIVE_MSECS,
                TimeUnit.MILLISECONDS,
                new BlockingBuffer<Runnable>(ThreadUtil.MAX_EXECUTOR_BUFF_SIZE),
                new ThreadFactory(){
                    @Override public Thread newThread(Runnable r) {
                        BasicThread t = ThreadUtil.create(r);
                        return t;
                    }
                })));

        initStopper();
    }
    
    public void initSsl() {
        SslContextFactory factory = new SslContextFactory();
        KeyStoreUtil ks = new KeyStoreUtil();
        factory.setKeyStorePath(ks.getKeyStorePath());
        factory.setKeyStorePassword(ks.getKeyStorePassword());
        factory.setKeyManagerPassword(ks.getKeyStorePassword());
        factory.setTrustStore(ks.getKeyStorePath());
        factory.setTrustStorePassword(ks.getKeyStorePassword());
        factory.setCertAlias(ks.getCertificateAlias());
        
        SslSelectChannelConnector connector = new SslSelectChannelConnector(factory);
        connector.setPort(getSslPort());
        _jetty.addConnector(connector);
    }
    
    public void addHandler(Handler handler) {
        _handlers.add(handler);
    }
    
    public void addPauseFilter (WebAppContext context) {
        context.addEventListener(new ServletRequestListener() {
            @Override
            public void requestInitialized(ServletRequestEvent reqEvt) {
                reqEvt.getServletRequest().setAttribute("rwLock", _ready);
            }

            @Override
            public void requestDestroyed(ServletRequestEvent reqEvt) {}
        });

        FilterHolder holder = new FilterHolder(PauseFilter.class);
        context.addFilter(holder, "/*", EnumSet.of(DispatcherType.REQUEST));
    }

    public void setLock (ConcurrentReady lock) {
        _ready = lock;
    }

    public void setUseWebXml(boolean useWebXml) {
        _useWebXml = useWebXml;
    }
    
    public void setServiceInstanceId (String serviceInstanceId) {
        _serviceInstanceId = serviceInstanceId;
    }

    public void mount(String contextPath, Responder responder) {
        DispatchServlet servlet = new DispatchServlet();
        servlet.setRootResponder(responder);
        
        ServletContextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
        context.setClassLoader(responder.getClass().getClassLoader());
        context.setContextPath(contextPath);
        context.addServlet(new ServletHolder(servlet), "/*");
        if (!StringUtil.isNullOrEmpty(_serviceInstanceId)) {
            FilterHolder filterHolder = new FilterHolder(ServiceInfoFilter.class);
            com.sencha.util.Configuration cfg = new com.sencha.util.Configuration();
            cfg.load(new File(Locator.getBaseFile(), "sencha.cfg"));
            filterHolder.setInitParameter("version", cfg.get("cmd.version", String.class));
            filterHolder.setInitParameter("instanceId", _serviceInstanceId);
            context.addFilter(filterHolder, "/*", EnumSet.allOf(DispatcherType.class));
        }
        addHandler(context);
    }
    
    public void mount(final String contextPath, final File file) {
        _logger.debug("Mounting {} into {}", file, contextPath);
        try {
            WebAppContext context = new WebAppContext();
            
            if (_useWebXml) {
                // Turn off MMF use on Windows or else files requested get "locked" and cannot be deleted
                context.setInitParameter("org.eclipse.jetty.servlet.Default.useFileMappedBuffer", "false");
                context.setContextPath(contextPath);
                context.setBaseResource(Resource.newResource(file));
            } else {
                // To fix SDKTOOLS-1062, instead of letting jetty load its configuration from
                // an existing WEB-INF/web.xml file, a specific configuration needs to be passed.
                // The configuration below comes from the webdefault.xml file included in jetty:
                //  jetty-webapp.jar/org/eclipse/jetty/webapp/webdefault.xml
                context.setConfigurations(new Configuration[]{
                    new AbstractConfiguration() {
                        @Override
                        public void configure(WebAppContext context) throws Exception {
                            // Turn off MMF use on Windows or else files requested get "locked" and cannot be deleted
                            context.setInitParameter("org.eclipse.jetty.servlet.Default.useFileMappedBuffer", "false");
                            context.setContextPath(contextPath);
                            context.setBaseResource(Resource.newResource(file));
                            context.addEventListener(new IntrospectorCleaner());
                            ServletHolder defaultServlet = context.addServlet("org.eclipse.jetty.servlet.DefaultServlet","/");
                            defaultServlet.setInitParameter("aliases","true");
                            defaultServlet.setInitParameter("acceptRanges","true");
                            defaultServlet.setInitParameter("dirAllowed","true");
                            defaultServlet.setInitParameter("welcomeServlets","false");
                            defaultServlet.setInitParameter("redirectWelcome","false");
                            defaultServlet.setInitParameter("maxCacheSize","256000000");
                            defaultServlet.setInitParameter("maxCachedFileSize","10000000");
                            defaultServlet.setInitParameter("maxCachedFiles","2048");
                            defaultServlet.setInitParameter("gzip","true");
                            defaultServlet.setInitParameter("useFileMappedBuffer", "true");
                            // The webdefault.xml file also included a jsp servlet and "index.jsp"
                            // in the welcome file list. These are not included on this configuration
                            // to avoid the common confusion on whether Sencha Cmd has support for jsp or not.
                            context.setWelcomeFiles(new String[] {
                                "index.html",
                                "index.htm"
                            });
                        }
                    }
                });
                addPauseFilter(context);
            }
            addHandler(context);
        } catch (Exception ex) {
            BasicException.raise(ex);
        }
    }
    
    private void initStopper() {
        ServletContextHandler context = new ServletContextHandler();
        context.setInitParameter("org.eclipse.jetty.servlet.Default.useFileMappedBuffer", "false");
        context.setContextPath("/.stop");
        context.addServlet(new ServletHolder(new HttpServlet() {
            @Override
            protected void doGet(HttpServletRequest request,
                                 HttpServletResponse response) throws IOException {
                response.setContentType("text/plain");
                response.getWriter().println(_stopper.getStopToken());
            }
        }), "/*");
        addHandler(context);
    }
    
    /**
     * 
     * @param contextPath
     * @param url
     */
    public void proxy(String contextPath, URL url) {
        ProxyServlet proxyServlet = new ProxyServlet.Transparent(contextPath, url.getProtocol(), url.getHost(), url.getPort(), url.getPath());
        
        ServletContextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
        context.setContextPath(contextPath);
        context.addServlet(new ServletHolder(proxyServlet), "/*");
        _handlers.add(context);
    }

    public void start () {
        ContextHandlerCollection contexts = new ContextHandlerCollection();
        contexts.setHandlers(_handlers.toArray(new Handler[_handlers.size()]));
        _jetty.setHandler(contexts);
        
        _stopper = new StopListener();
        _stopper.start(); // oh the irony

        try {
            _jetty.addLifeCycleListener(new LifeCycle.Listener() {
                @Override
                public void lifeCycleStarting(LifeCycle lifeCycle) {
                    starting();
                }

                @Override
                public void lifeCycleStarted(LifeCycle lifeCycle) {
                    started();
                }

                @Override
                public void lifeCycleFailure(LifeCycle lifeCycle, Throwable throwable) {
                    error(throwable);
                }

                @Override
                public void lifeCycleStopping(LifeCycle lifeCycle) {
                    stopping();
                }

                @Override
                public void lifeCycleStopped(LifeCycle lifeCycle) {
                    stopped();
                }
            });
            _jetty.start();
        } catch (Exception ex) {
            BasicException.raise(ex);
            if (ex instanceof BindException) {
                throw BasicException.wrap(ex);
            }
        }
    }

    public Integer getPort() {
        return _port;
    }

    private boolean _starting = false;
    private boolean _started = false;
    private boolean _stopping = false;
    private boolean _stopped = false;
    private Throwable _error = null;

    private synchronized void starting() { _starting = true; notifyAll();}
    private synchronized void started() { _started = true; notifyAll();}

    private synchronized void error(Throwable err) {
        _error = err;
        notifyAll();
    }

    private synchronized void stopping() { _stopping = true; notifyAll();}
    private synchronized void stopped() { _stopped = true; notifyAll();}

    public synchronized void waitForStarting() {
        while(!_starting && getError() == null) {
            ThreadUtil.wait(this);
        }
    }

    public synchronized void waitForStarted() {
        while(!_started && getError() == null) {
            ThreadUtil.wait(this);
        }
    }
    public synchronized void waitForStopping() {
        while(!_stopping && getError() == null) {
            ThreadUtil.wait(this);
        }
    }

    public synchronized void waitForStopped() {
        while(!_stopped && getError() == null) {
            ThreadUtil.wait(this);
        }
    }

    public synchronized int getJettyPort() {
        int port = -1;
        for (org.eclipse.jetty.server.Connector conn : _jetty.getServer().getConnectors()) {
            _logger.debug("port : {} , local port : {} , integral port : {} , confidential port {}", new Object[]{
                conn.getPort(),
                conn.getLocalPort(),
                conn.getIntegralPort(),
                conn.getConfidentialPort()});
            port = conn.getLocalPort();
            // just need the first one
            break;
        }

        return port;
    }
    
    public synchronized int getJettyHttpsPort() {
        int port = -1;
        for (org.eclipse.jetty.server.Connector conn : _jetty.getServer().getConnectors()) {
            if (conn instanceof SslSelectChannelConnector) {
                _logger.debug("port : {} , local port : {} , integral port : {} , confidential port {}", new Object[]{
                    conn.getPort(),
                    conn.getLocalPort(),
                    conn.getIntegralPort(),
                    conn.getConfidentialPort()});
                port = conn.getLocalPort();
                // just need the first one
                break;
            }
        }
        return port;
    }
    
    public Throwable getError() {
        return _error;
    }

    public void setError(Throwable error) {
        _error = error;
    }

    public int getSslPort() {
        return _sslPort;
    }

    public void setSslPort(int sslPort) {
        _sslPort = sslPort;
    }

    public static class ServiceInfoFilter implements Filter {
        private String version;
        private String instanceId;
        
        @Override
        public void init(FilterConfig filterConfig) throws ServletException {
            version = filterConfig.getInitParameter("version");
            instanceId = filterConfig.getInitParameter("instanceId");
        }

        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
            HttpServletResponse httpServletResponse = (HttpServletResponse) response;
            httpServletResponse.addHeader("X-SenchaCmd-Version", version);
            httpServletResponse.addHeader("X-SenchaCmd-InstanceId", instanceId);
            chain.doFilter(request, response);
        }

        public void destroy() {
        }
    }

    public static class PauseFilter implements Filter {
        @Override
        public void init(FilterConfig filterConfig) throws ServletException {
        }

        public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
            HttpServletRequest httpRequest = (HttpServletRequest) request;
            String path = httpRequest.getServletPath();
            ConcurrentReady lock = null;
            if (path.endsWith(".css") || path.endsWith(".json")) {
                lock = (ConcurrentReady) request.getAttribute("rwLock");
            }
            if (lock != null) {
                lock.waitFor();
            }
            chain.doFilter(request, response);
        }

        public void destroy() {
        }
    }

    private static final Logger _logger = SenchaLogManager.getLogger();

    private ConcurrentReady _ready;

    private final org.eclipse.jetty.server.Server _jetty;
    private final List<Handler> _handlers = new ArrayList<Handler>();
    private StopListener _stopper;
    private Integer _port;
    private InetSocketAddress _address;
    private boolean _useWebXml;
    private String _serviceInstanceId;
    private int _sslPort;
}
