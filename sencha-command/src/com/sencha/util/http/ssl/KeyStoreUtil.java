/*
 * Copyright (c) 2012-2017. Sencha Inc.
 */

package com.sencha.util.http.ssl;

import com.sencha.exceptions.BasicException;
import com.sencha.util.FileUtil;
import com.sencha.util.Locator;
import com.sencha.util.ObjectUtil;
import com.sencha.util.PathUtil;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.x509.X509V1CertificateGenerator;

import javax.security.auth.x500.X500Principal;
import java.io.FileOutputStream;
import java.math.BigInteger;
import java.security.*;
import java.security.cert.X509Certificate;
import java.util.Date;

// http://www.pixelstech.net/article/**********-Generate-cetrificate-in-Java----3
// http://stackoverflow.com/questions/1615871/creating-an-x509-certificate-in-java-without-bouncycastle

public class KeyStoreUtil {
    
    private String _keyStorePath = Locator.getUserDir("ssl.keystore.jks").getAbsolutePath();
    private char[] _keyStorePassword = "ssl.keystore.jks".toCharArray();
    private String _certificatePath = Locator.getUserDir("SenchaCmd-self-signed.cert").getAbsolutePath();

    private String _certificateAlias = "jetty";
    
    public KeyStoreUtil() {
        PathUtil.ensurePathExists(PathUtil.getParentFile(_keyStorePath), true);
        initialize();
    }
        
    
    public void initialize() {
        String keyStorePath = _keyStorePath;
        KeyStore store;        
        
        // ensure the key store is present
        if (!PathUtil.exists(keyStorePath)) {
            store = createKeyStore();
            FileOutputStream stream = null;
            try {
                
                KeyPair keyPair = createKeyPair();
                PrivateKey privateKey = keyPair.getPrivate(); 
                X509Certificate cert = createSignedCertificate(keyPair);
                
                store.setKeyEntry(
                    getCertificateAlias(), 
                    privateKey,
                    _keyStorePassword,
                    new X509Certificate[]{
                        cert
                    });                
                
                stream = new FileOutputStream(keyStorePath);
                store.store(stream, _keyStorePassword);
                stream.close();
            }
            catch (Exception ex) {
                if (stream != null) {
                    try {
                        stream.close();
                    }
                    catch (Exception e) {
                        ObjectUtil.ignore(e);
                    }
                }
            }
        }
    }
    
    public KeyStore createKeyStore() {
        try {
            KeyStore ks = KeyStore.getInstance(KeyStore.getDefaultType());

            char[] password = _keyStorePassword;
            ks.load(null, password);
            return ks;
        }
        catch (Exception ex) {
            throw BasicException.raise(ex);
        }
    } 
    
    
    public KeyPair createKeyPair() {
        try {
            KeyPairGenerator keyGen = KeyPairGenerator.getInstance("RSA");
            keyGen.initialize(1024);
            KeyPair keypair = keyGen.generateKeyPair();
            return keypair;
        }
        catch (Exception ex) {
            throw BasicException.raise(ex);
        }
    }

    private X509Certificate createSignedCertificate(KeyPair keyPair) {
        try {
            Security.addProvider(new BouncyCastleProvider());
            Date startDate = new Date(System.currentTimeMillis());
            Date expiryDate = new Date(startDate.getTime() + (5 * 365 * 86400000l));
            BigInteger serialNumber = new BigInteger(64, new SecureRandom());
            X509V1CertificateGenerator certGen = new X509V1CertificateGenerator();
            X500Principal dnName = new X500Principal("CN=localhost, O=Sencha, OU=Cmd, L=Auto Generated Sencha Cmd Self-Signed Certificate");
            certGen.setSerialNumber(serialNumber);
            certGen.setIssuerDN(dnName);
            certGen.setNotBefore(startDate);
            certGen.setNotAfter(expiryDate);
            certGen.setSubjectDN(dnName);
            certGen.setPublicKey(keyPair.getPublic());
            certGen.setSignatureAlgorithm("SHA256withRSA");
            X509Certificate cert = certGen.generate(keyPair.getPrivate(), "BC");
            FileUtil.writeFileData(_certificatePath, cert.getEncoded());
            return cert;
        }
        catch(Exception ex){
            ex.printStackTrace();
        }
        return null;
    }

    public String getKeyStorePath() {
        return _keyStorePath;
    }
    
    public String getKeyStorePassword() {
        return new String(_keyStorePassword);
    }

    public String getCertificateAlias() {
        return _certificateAlias;
    }

    public void setCertificateAlias(String certificateAlias) {
        _certificateAlias = certificateAlias;
    }
}
