/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.util;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.sencha.exceptions.BasicException;
import java.io.File;
import java.io.IOException;
import java.io.Reader;
import java.lang.reflect.Modifier;
import java.lang.reflect.Type;
import java.util.*;

public class JsonUtil {
    public static final class EnumAdapterFactory implements TypeAdapterFactory {
        @SuppressWarnings("unchecked")
        @Override
        public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
            Class<? super T> rawType = type.getRawType();
            if (!Enum.class.isAssignableFrom(rawType) || rawType == Enum.class) {
              return null;
            }
            if (!rawType.isEnum()) {
              rawType = rawType.getSuperclass(); // handle anonymous subclasses
            }
            return (TypeAdapter<T>) new EnumTypeAdapter(rawType);
        }
    }
    public static final class EnumTypeAdapter extends TypeAdapter<Enum<?>> {
        private final Class<Enum<?>> _type;
        @SuppressWarnings("unchecked")
        public EnumTypeAdapter(Class<?> type) {
            _type = (Class<Enum<?>>) type;
        }

        @Override
        public void write(JsonWriter out, Enum<?> value) throws IOException {
            if(value == null) {
                out.nullValue();
            } else {
                out.value(value.name().toLowerCase());
            }
        }

        @Override
        public Enum<?> read(JsonReader in) throws IOException {
            String string = in.nextString();
            Enum<?>[] values = _type.getEnumConstants();
            for (Enum<?> val : values) {
                if (val.name().toLowerCase().equals(string.toLowerCase())) {
                    return val;
                }
            }
            System.out.println("Read " + string + ", couldn't find a match in " + values);
            return null;
        }
    }
    public static final String FILE_EXTENSION = ".json";
    public static final String FILE_TYPE = "json";

    public static void toJson (Object obj, Appendable writer) {
        _gson.toJson(obj, writer);
    }
    
    public static String toJson (Object obj) {
        return _gson.toJson(obj);
    }

    public static String toJson (Object obj, Type type) {
        return _gson.toJson(obj, type);
    }

    public static JsonElement toJsonTree (Object obj) {
        return _gson.toJsonTree(obj);
    }

    public static JsonElement toJsonTree (Object obj, Type type) {
        return _gson.toJsonTree(obj, type);
    }

    public static String toJson (Object obj, boolean pretty) {
        ThreadSafeGson gson = pretty ? _gsonPretty : _gson;
        return gson.toJson(obj);
    }

    public static String toJson (Object obj, Type type, boolean pretty) {
        ThreadSafeGson gson = pretty ? _gsonPretty : _gson;
        return gson.toJson(obj, type);
    }

    public static void toJsonPublicOnly(Object object, Appendable writer) {
        _gsonPublicOnly.toJson(object, writer);
    }
    
    public static String toJsonPublicOnly(Object object, boolean pretty) {
        ThreadSafeGson gson = pretty ? _gsonPrettyPublicOnly : _gsonPublicOnly;
        return gson.toJson(object);
    }
    
    public static String toJsonPrettyPublicOnly(Object object) {
        return toJsonPublicOnly(object, true);
    }
    
    public static String toJsonPretty (Object obj) {
        return _gsonPretty.toJson(obj);
    }

    public static String toJsonPretty (JsonElement obj) {
        return _gsonPretty.toJson(obj);
    }

    public static byte[] toJsonUtf8 (Object obj) {
        return StringUtil.getBytes(_gson.toJson(obj), "UTF-8");
    }

    public static byte[] toJsonUtf8 (Object obj, boolean pretty) {
        return StringUtil.getBytes(toJson(obj, pretty), "UTF-8");
    }

    public static byte[] toJsonUtf8Pretty (Object obj) {
        return StringUtil.getBytes(_gsonPretty.toJson(obj), "UTF-8");
    }

    public static <T> T fromJson (Reader reader, Type type) {
        return _gson.fromJson(reader, type);
    }

    public static <T> T fromJsonWithClassType (String json, Class<T> type) {
        return _gson.fromJson(json, getTypeToken(type));
    }

    public static <T> T fromJson (String json, Type type) {
        return _gson.fromJson(json, type);
    }

    public static <T> T fromJson (JsonElement json, Type type) {
        return _gson.fromJson(json, type);
    }

    public static <T> T fromJson (byte[] jsonBytes, Class<T> cls) {
        String json = CharsetDetector.decodeBytes(jsonBytes);
        return fromJson(json, cls);
    }

    public static <T> T fromJson(String json, Class<T> cls) {
        return _gson.fromJson(json, cls);
    }
    
    public static <T> T loadJsonWithClass(File file, Class<T> cls) {
        return loadJson(file, getTypeToken(cls));
    }
    
    public static <T> T loadJson (File file, Type type) {
        return loadJson(file, type, null);
    }
    
    public static <T> T loadJson (File file, Type type, Configuration cfg) {
        String json = FileUtil.readFile(file);
        if(cfg != null) {
            json = cfg.jsonEncodeStrings().evaluatePropertyReferences(json);
        }
        try {
            return fromJson(json, type);
        } catch (Exception ex) {
            throw new BasicException(ex, "Failed to load JSON from {0} - {1}", 
                                     PathUtil.getCanonicalPath(file),
                                     ex.getMessage()).raise();
        }
    }
    
    public static <T> T loadJson (File file, Class<T> cls) {
        return loadJson(file, cls, null);
    }

    public static <T> T loadJson (File file, Class<T> cls, Configuration cfg) {
        String json = FileUtil.readFile(file);
        if (file.getName().equals("app.json")) {
            if (json.contains("\"\\.svn$\"")) {
                json = json.replace("\"\\.svn$\"", "\"\\\\.svn$\"");
            }
        }
        if(cfg != null) {
            json = cfg.jsonEncodeStrings().evaluatePropertyReferences(json);
        }
        try {
            return fromJson(json, cls);
        } catch (Exception ex) {
            throw new BasicException(ex, "Failed to load JSON from {0} - {1}", 
                                     PathUtil.getCanonicalPath(file),
                                     ex.getMessage()).raise();
        }
    }

    public static Map<String, Object> loadJsonMap(File file, Configuration cfg) {
        return loadJson(file, Map.class, cfg);
    }

    public static Map<String, Object> loadJsonMap(File file) {
        return loadJson(file, Map.class);
    }

    public static <T> Type getTypeToken(Class<T> clazz) {
        return TypeToken.get(clazz).getType();
    }
    
    public static void saveJson (Object object, File file, boolean pretty) {
        String json = toJson(object, pretty);
        FileUtil.writeFile(file, json);
    }

    public static void saveJson (Object object, File file) {
        saveJson(object, file, true);
    }
    
    public static Map<String, Object> toMap(Object obj) {
        return toMap(obj, obj.getClass());
    }
    
    public static Map<String, Object> toMap(Object obj, Type type) {
        return toMap(toJson(obj, type));
    }
    
    public static Map<String, Object> toMap(String json) {
        Map<String, Object> data = new LinkedHashMap<String, Object>();
        return fromJson(json, data.getClass());
    }
    
    public static JsonElement toJsonTree(String jsonData) {
        JsonParser parser = new JsonParser();
        return (JsonElement)parser.parse(jsonData);
    }

    private static ThreadSafeGson _gson;
    private static ThreadSafeGson _gsonPretty;
    private static ThreadSafeGson _gsonPublicOnly;
    private static ThreadSafeGson _gsonPrettyPublicOnly;

    private static final List<TypeAdapterFactory> _typeAdapterFactories
        = new ArrayList<TypeAdapterFactory>(){{
            add(new EnumAdapterFactory());
        }};
    
    private static final List<Pair<Class<?>, Object>> _typeAdapters
        = new ArrayList<Pair<Class<?>, Object>>();
    
    private static GsonBuilder registerTypes(GsonBuilder builder) {
        builder.registerTypeAdapter(Double.class, new JsonSerializer<Double>() {
            public JsonElement serialize(Double value, Type theType,
                                         JsonSerializationContext context) {
                if (value.isNaN()) {
                    return new JsonPrimitive(0); // Convert NaN to zero
                }

                // This gets rid of ".0" on all numbers and makes round-trips from/to
                // JSON possible. Without this, we might read something like:
                //
                //      foo: 42
                //
                // But write this:
                //
                //      foo: 42.0
                //
                double x = value.doubleValue();
                long y = (long) x;
                if (x == y) {
                    return new JsonPrimitive(y);
                }

                return new JsonPrimitive(value);
            }
        });

        for(TypeAdapterFactory factory : _typeAdapterFactories) {
            builder.registerTypeAdapterFactory(factory);
        }
        for(Pair<Class<?>, Object> adapter : _typeAdapters) {
            builder.registerTypeHierarchyAdapter(adapter.getLeft(), adapter.getRight());
        }
        return builder;
    }
    
    public static synchronized void addTypeAdapters(Collection<Pair<Class<?>, Object>> adapters) {
        for(Pair<Class<?>, Object> adapter : adapters) {
            _typeAdapters.add(adapter);
        }
        initialize();
    }
    
    public static synchronized void addTypeAdapter(final Class<?> type, final Object adapter) {
        addTypeAdapters(new ArrayList<Pair<Class<?>, Object>>(){{
            add(new Pair<Class<?>, Object>(type, adapter));
        }});
    }

    public static synchronized void addTypeAdapterFactories(Collection<TypeAdapterFactory> factories) {
        for(TypeAdapterFactory factory : factories) {
            _typeAdapterFactories.add(factory);
        }
        initialize();
    }
    
    public static synchronized void addTypeAdapterFactory(final TypeAdapterFactory factory) {
        addTypeAdapterFactories(new ArrayList<TypeAdapterFactory>(){{
            add(factory);
        }});
    }
    
    static {
        initialize();
    }

    private static synchronized void initialize() {
        _gson = createGson();
        _gsonPretty = createGsonPretty();
        _gsonPublicOnly = createGsonPublicOnly();
        _gsonPrettyPublicOnly = createGsonPublicOnlyPretty();
    }

    public static GsonBuilder createDefaultBuilder() {
        GsonBuilder gson = registerTypes(new GsonBuilder());
        gson.setLenient();
        return gson;
    }
    
    public static GsonBuilder createPublicOnlyBuilder() {
        GsonBuilder gson = registerTypes(new GsonBuilder());
        gson.registerTypeAdapterFactory(new EnumAdapterFactory());
        gson.excludeFieldsWithModifiers(
                Modifier.STATIC, 
                Modifier.TRANSIENT, 
                Modifier.VOLATILE, 
                Modifier.PRIVATE,
                Modifier.PROTECTED);
        gson.setLenient();
        return gson;
    }
    
    public static ThreadSafeGson createGson() {
        GsonBuilder gson = createDefaultBuilder();
        return new ThreadSafeGson(gson.create());
    }
    
    public static ThreadSafeGson createGsonPretty() {
        GsonBuilder gson = createDefaultBuilder();
        return new ThreadSafeGson(gson.setPrettyPrinting().create());
    }
    
    public static ThreadSafeGson createGsonPublicOnly() {
        GsonBuilder gson = createPublicOnlyBuilder();
        return new ThreadSafeGson(gson.create());
    }
    
    public static ThreadSafeGson createGsonPublicOnlyPretty() {
        GsonBuilder gson = createPublicOnlyBuilder();
        return new ThreadSafeGson(gson.setPrettyPrinting().create());
    }
    
    public static class ThreadSafeGson {
        public ThreadSafeGson(Gson gson) {
            _gson = gson;
        }

        public synchronized void toJson(Object src, Appendable writer) {
            if(src instanceof JsonElement) {
                _gson.toJson((JsonElement)src, writer);
            } else {
                _gson.toJson(src, writer);
            }
        }

        public synchronized String toJson(Object src) {
            if(src instanceof JsonElement) {
                return _gson.toJson((JsonElement)src);
            } else {
                return _gson.toJson(src);
            }
        }

        public synchronized String toJson(Object src, Type type) {
            if(src instanceof JsonElement) {
                return _gson.toJson((JsonElement)src, type);
            } else {
                return _gson.toJson(src, type);
            }
        }

        public synchronized <T> T fromJson(Reader json, Type typeOfT) {
            return _gson.fromJson(json, typeOfT);
        }

        public synchronized <T> T fromJson(String json, Type typeOfT) {
            return _gson.fromJson(json, typeOfT);
        }
        
        public synchronized <T> T fromJson(JsonElement json, Type typeOfT) {
            return _gson.fromJson(json, typeOfT);
        }
        
        public synchronized JsonElement toJsonTree(Object obj) {
            return _gson.toJsonTree(obj);
        }
        
        public synchronized JsonElement toJsonTree(Object obj, Type type) {
            return _gson.toJsonTree(obj, type);
        }
        
        private Gson _gson;
    }

}
