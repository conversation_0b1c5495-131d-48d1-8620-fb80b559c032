/*
 * Copyright (c) 2012-2015. Sencha Inc.
 */

package com.sencha.util;

import com.sencha.exceptions.ExArg;
import com.sencha.logging.SenchaLogManager;
import com.sencha.util.http.RequestLogger;
import com.sencha.util.http.Responder;
import com.sencha.util.http.Server;
import org.eclipse.jetty.server.Handler;
import org.eclipse.jetty.server.handler.RequestLogHandler;
import org.slf4j.Logger;

import java.io.File;
import java.util.*;

public class JettyServer {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private Server _server;
    private Stopper _stopper;
    private String _log;
    private int _port;
    private int _sslPort = -1;
    private String _only;
    private boolean _background;
    private boolean _useWebXml;
    private String _map;
    private List<String> _hosts = new ArrayList<String>();
    private String _host;
    private List<Handler> _handlers = new ArrayList<Handler>();
    
    private Map<String, Responder> _responders = new LinkedHashMap<String, Responder>();
    
    public String getHost() {
        if(_host == null) {
            _host = "http://localhost:" + getPort();
        }
        return _host;
    }

    private synchronized void init() {
        if(_server == null) {
            _logger.info("Starting server on port : {}", _port);
            _server = new Server(_port);
            if (_sslPort > -1) {
                _server.setSslPort(_sslPort);
                _server.initSsl();
            }
            _server.setUseWebXml(_useWebXml);
            _stopper = new StopListener().start(); // oh the irony
            Set<String> names = new HashSet<String>();
            String host = getHost();

            for (Handler handler : _handlers) {
                _server.addHandler(handler);
            }

            if (getLog() != null) {
                RequestLogHandler logHandler = new RequestLogHandler();
                RequestLogger requestLog;
                if (getLog().isEmpty() || getLog().equals(".")) {
                    requestLog = new RequestLogger();
                } else {
                    requestLog = new RequestLogger(getLog());
                }
                if (getOnly() != null) {
                    requestLog.setWhiteList(getOnly());
                }
                requestLog.setAppend(true);
                requestLog.setExtended(false);
                logHandler.setRequestLog(requestLog);
                _server.addHandler(logHandler);
            }

            for (String path : _map.split(",")) {
                int sep = path.indexOf('=');
                String contextPath = "/";
                String localPath = path;

                if (sep > 0) {
                    contextPath = "/" + path.substring(0, sep);
                    localPath = path.substring(sep+1);
                }

                String hostFullName = host + contextPath;
                _hosts.add(hostFullName + (!hostFullName.endsWith("/") ? "/" : ""));

                _logger.info("Mapping {} to {}...", hostFullName, localPath);
                if (!names.add(contextPath)) {
                    new ExArg("Multiple uses of name: {0}", contextPath).raise();
                }

                _server.mount(contextPath, new File(localPath));
            }    
            
            for(String key : _responders.keySet()) {
                _server.mount(key, _responders.get(key));
            }
        }
        notifyAll();
    }

    public void setLock (ConcurrentReady lock) {
        _server.setLock(lock);
    }

    public void start() {
        init();
        _server.start();
        _logger.info("Server started at port : {}", getJettyPort());
        int httpsPort = getJettyHttpsPort();
        if (httpsPort > -1) {
            _logger.info("HTTPS Server started at port : {}", httpsPort);
        }
        if(!isBackground()) {
            _server.waitForStop();
        }
    }

    public void stop() {
        if (_stopper != null) {
            _stopper.stop();
        }
        if(_server != null) {
            _server.stop();
        }
    }
    
    
    public String getLog() {
        return _log;
    }

    public void setLog(String log) {
        _log = log;
    }

    public int getPort() {
        return _port;
    }

    public void setPort(int port) {
        _port = port;
    }

    public int getSslPort() {
        return _sslPort;
    }

    public void setSslPort(int port) {
        _sslPort = port;
    }

    public String getOnly() {
        return _only;
    }

    public void setOnly(String only) {
        _only = only;
    }

    public boolean isBackground() {
        return _background;
    }

    public void setBackground(boolean background) {
        _background = background;
    }

    public String getMap() {
        return _map;
    }

    public void setMap(String map) {
        _map = map;
    }
    
    public Stopper getStopper() {
        return _stopper;
    }
    
    public void mount(String path, Responder responder) {
        _responders.put(path, responder);
    }

    public int getJettyPort() {
        return _server.getJettyPort();
    }

    public int getJettyHttpsPort() {
        return _server.getJettyHttpsPort();
    }
    
    public void setHandlers(List<Handler> handlers) {
        _handlers = handlers;
    }

    public synchronized void waitForStarted() {
        while(_server == null) {
            ThreadUtil.wait(this);
        }
        _server.waitForStarted();
    }
    
    public synchronized void waitForStopped() {
        if(_server != null) {
            _server.waitForStopped();
        }
    }

    public void setUseWebXml (boolean useWebXml) {
        _useWebXml = useWebXml;
    }

    public Throwable getError() {
        if (_server != null) {
            return _server.getError();
        }
        return null;
    }
}
