/*
 * Copyright (c) 2012-2013. Sencha Inc.
 */
package com.sencha.test;

import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.text.MessageFormat;
import org.eclipse.jetty.util.resource.Resource;
import org.eclipse.jetty.webapp.WebAppContext;
import java.net.MalformedURLException;
import org.eclipse.jetty.server.handler.ContextHandlerCollection;
import java.net.URL;
import java.util.HashMap;
import org.eclipse.jetty.server.Server;
import org.junit.AfterClass;
import com.sencha.security.Signature;
import com.sencha.security.Certificate;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import com.sencha.exceptions.ExNotFound;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.util.HashSet;
import java.util.Set;
import com.sencha.logging.SenchaLogManager;
import org.slf4j.Logger;
import java.util.Map;
import com.sencha.exceptions.BasicException;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.util.*;
import static com.sencha.util.JsonUtil.toJson;
import static com.sencha.util.StringUtil.escapeForLogging;
import static com.sencha.util.StringUtil.normalizeLineEndings;
import java.io.File;
import java.util.regex.Pattern;

import static com.sencha.util.StringUtil.removeDuplicateLineEndings;
import static junit.framework.Assert.assertEquals;
import org.junit.Test;

import static org.junit.Assert.*;

public class TestBase {
    @Test public void testNOP () {
        // nothing test to allow derived classes to disable all their tests...
    }

    public static File getTestFilesDir (Class cls, String sub) {
        File dir = new File(Locator.getTestDir(), cls.getName());
        return (sub == null) ? dir : new File(dir, sub);
    }

    public static File getTestFilesDir (Class cls) {
        return getTestFilesDir(cls, null);
    }

    public static File getTestTempDir (Class cls, String sub) {
        File dir = new File(Locator.getTestTempDir(), cls.getName());
        return (sub == null) ? dir : new File(dir, sub);
    }

    public static File getTestTempDir (Class cls) {
        return getTestTempDir(cls, null);
    }

    //-------------------------------------------------------------------------

    public File getTestFilesDir (String sub) {
        return getTestFilesDir(getClass(), sub);
    }

    public File getTestFilesDir () {
        return getTestFilesDir((String) null);
    }

    public File getTestTempDir (String sub) {
        return getTestTempDir(getClass(), sub);
    }

    public File getTestTempDir () {
        return getTestTempDir((String) null);
    }

    //-------------------------------------------------------------------------

    public static String getPath(String path, int depth) {
        try {
            StackTraceElement[] st = Thread.currentThread().getStackTrace();
            String className = st[depth].getClassName();
            return ReflectionUtil.getPath(path, className);
        } catch (Exception ex) {
            throw new BasicException(ex).raise();
        }
    }

    public static String getPath(String path) {
        return getPath(path, 3);
    }

    public CompilerContext getNewContext(String[] classpaths) {
        CompilerContext ctx = new CompilerContext(new Configuration());
        ctx.createClassPathScope(classpaths);
        return ctx;
    }

    public boolean inRange(int src, int tst, int range) {
        if (Math.abs(src - tst) > range) {
            return false;
        }
        return true;
    }

    public static final class CertChecker implements Certificate.Check {
        public String summary = "";

        @Override
        public void invalid (int index, Signature sig, Certificate signer) {
            summary += "I";
        }

        @Override
        public void notSigned () {
            summary += "N";
        }

        @Override
        public void skipped (int index, Signature sig) {
            summary += "S";
        }

        @Override
        public void valid (int index, Signature sig, Certificate signer) {
            summary += "V";
        }
    }

    public static String checkCert (Certificate cert, Certificate.Store store) {
        CertChecker chk = new CertChecker();

        cert.check(store, chk);

        return chk.summary;
    }

    public static String checkCert (Certificate cert) {
        return checkCert(cert, null);
    }

    //-------------------------------------------------------------------------

    public static interface Inspector {
        void inspect (FileChecker file);
    }

    public static abstract class StreamInspector implements Inspector {
        @Override
        public final void inspect (FileChecker file) {
            _file = file;
            InputStream content = file.openInputStream();
            try {
                inspect(content);
            } finally {
                StreamUtil.close(content);
            }
        }

        public FileChecker getFile () {
            return _file;
        }

        public abstract void inspect (InputStream content);

        private FileChecker _file;
    }

    public static abstract class ContentInspector extends StreamInspector {
        @Override
        public final void inspect (InputStream in) {
            String content = StreamUtil.readAll(in);
            inspect(content);
        }

        public abstract void inspect (String content);
    }

    public static abstract class LinesInspector extends ContentInspector {
        @Override
        public final void inspect (String content) {
            String[] lines = content.split("[\n\r]+");
            inspect(lines);
        }

        public abstract void inspect (String... lines);
    }

    public static class HasLinesInspector extends LinesInspector {
        public HasLinesInspector (String... lines) {
            for (String line : lines) {
                LineState ls = _expected.get(line);
                if (ls == null) {
                    _expected.put(line, ls = new LineState(line));
                }
                ls.expect();
            }
        }

        public HasLinesInspector ignore (String... patterns) {
            for (String pat : patterns) {
                Pattern regex = Pattern.compile(pat);
                _ignores.add(regex);
            }
            return this;
        }

        public HasLinesInspector ignore (Pattern... patterns) {
            _ignores.addAll(Arrays.asList(patterns));
            return this;
        }

        public HasLinesInspector mustHaveAll () {
            _mustHaveAll = true;
            return this;
        }
        
        @Override
        public void inspect (String... actual) {
            for (String line : actual) {
                LineState ls = _expected.get(line);
                if (ls == null) {
                    if (!isIgnored(line)) {
                        fail(getFile().getName() + " should not contain: " + line);
                    }
                } else {
                    ls.found();
                }
            }

            for (LineState ls : _expected.values()) {
                ls.test(_mustHaveAll);
            }
        }

        private boolean isIgnored (String line) {
            for (Pattern regex : _ignores) {
                if (regex.matcher(line).matches()) {
                    return true;
                }
            }

            return false;
        }

        private class LineState {
            LineState (String text) {
                _text = text;
            }

            void expect () {
                ++_expected;
            }

            void found () {
                ++_actual;
            }

            void test (boolean mustHaveAll) {
                if (mustHaveAll ? _expected != _actual : _actual < _expected) {
                    fail(MessageFormat.format("{0} has {1} but should have {2} of: {3}", 
                               getFile().getName(), _actual, _expected, _text));
                }
            }

            private final String _text;
            private int _expected = 0;
            private int _actual = 0;
        }

        private final Map<String, LineState> _expected = new HashMap();
        private final List<Pattern> _ignores = new ArrayList();
        private boolean _mustHaveAll = false;
    }

    public static class ExactLinesInspector extends LinesInspector {
        public ExactLinesInspector (String... lines) {
            _expected = lines;
        }

        @Override
        public void inspect (String[] actual) {
            int n = Math.min(actual.length, _expected.length);
            
            for (int i = 0; i < n; ++i) {
                if (_expected[i] != null && !_expected[i].equals(actual[i])) {
                    String prefix = getFile().getName() + ":" + (i+1) + " - ";
                    // this play best in good IDE's that show the diff:
                    assertEquals(prefix + _expected[i], prefix + actual[i]);
                }
            }

            int delta = _expected.length - actual.length;
            if (delta > 0) {
                fail("Actual file is missing " + delta + " lines");
            } else if (delta < 0) {
                fail("Actual file has " + -delta + " extra lines");
            }
        }

        private final String[] _expected;
    }

    public static abstract class ObjectInspector<T> extends ContentInspector {
        public ObjectInspector (Class<T> cls) {
            _cls = cls;
        }

        @Override
        public void inspect (String content) {
            T object = JsonUtil.fromJson(content, _cls);
            inspect(object);
        }

        public abstract void inspect (T object);

        private Class<T> _cls;
    }

    public static class FileChecker {
        public FileChecker (File file) {
            this(null, file.getAbsolutePath());
        }

        public boolean exists () {
            return getFile().exists();
        }

        public File getFile () {
            return new File(_path);
        }

        public String getName () {
            return _path;
        }

        public boolean isDirectory () {
            return getFile().isDirectory();
        }

        public boolean isFile () {
            return getFile().isFile();
        }

        public FileChecker inspect (Inspector... inspectors) {
            for (Inspector inspector : inspectors) {
                inspector.inspect(this);
            }
            return this;
        }

        public FileChecker inspect (String... lines) {
            new ExactLinesInspector(lines).inspect(this);
            return this;
        }

        public InputStream openInputStream () {
            try {
                return new FileInputStream(getFile());
            }
            catch (FileNotFoundException ex) {
                throw new ExNotFound(ex, "File {0} not found", _path);
            }
        }

        public final FileChecker toBeFolder () {
            if (!isDirectory()) {
                fail("Expected '" + getName() + "' to be a folder.");
            }
            return this;
        }

        public final FileChecker toBeFile () {
            if (!isFile()) {
                fail("Expected '" + getName() + "' to be a file.");
            }
            return this;
        }

        public final FileChecker toExist () {
            if (!exists()) {
                fail("Expected '" + getName() + "' to exist.");
            }
            return this;
        }

        public class With {
            public FileChecker folder (String name) {
                pull(name, _folders);
                FileChecker fc = descend(this, name);
                return fc;
            }

            public FileChecker file (String name) {
                pull(name, _files);
                FileChecker fc = descend(this, name);
                return fc;
            }

            public ZipFileChecker zip (String name) {
                ZipFileChecker fc = new ZipFileChecker(this, name);
                return fc;
            }

            public FileChecker end () {
                _logger.debug("Finished checking contents of file/folder: {}", getName());
                return FileChecker.this;
            }

            private String getName () {
                return FileChecker.this.getName();
            }

            private void pull (String name, Set<String> map) {
                if (!map.remove(name)) {
                    String kind = (map == _files) ? "file" : "folder";
                    fail("Expected "+kind+" '"+name+"' in " + getName());
                }
            }

            private With () {
                _logger.debug("Checking contents of file/folder: {}", getName());
                scan(_files, _folders);
            }

            protected final Set<String> _files = new HashSet();
            protected final Set<String> _folders = new HashSet();
        }
        
        public class WithOnly extends With {
            @Override
            public FileChecker end () {
                if (_files.size() + _folders.size() > 0) {
                    String message = getName() + " contains ";
                    if (!_folders.isEmpty()) {
                        message += "unexpected folders: " + StringUtil.join(_folders, ", ");
                    }
                    if (!_files.isEmpty()) {
                        if (!_folders.isEmpty()) {
                            message += " and ";
                        }
                        message += "unexpected files: " + StringUtil.join(_files, ", ");
                    }

                    fail(message);
                }

                return super.end();
            }
        }

        public With and () {
            _logger.debug("Returning to parent file/folder: {}", _parent.getName());
            return _parent;
        }

        public FileChecker end () {
            return _parent.end();
        }

        public With with () {
            return new With();
        }

        public WithOnly withOnly () {
            return new WithOnly();
        }

        protected FileChecker (With parent, String name) {
            _parent = parent;
            _path = (parent == null) ? name : (parent.getName() + "/" + name);
            _logger.debug("Checking file/folder: {}", _path);
        }

        protected FileChecker descend (With parent, String name) {
            return new FileChecker(parent, name);
        }

        protected void scan (Set<String> files, Set<String> folders) {
            File[] sub = new File(_path).listFiles();
            for (File f : sub) {
                (f.isDirectory() ? folders : files).add(f.getName());
            }
        }

        private final String _path;
        private final With _parent;
    }

    public static class ZipFileChecker extends FileChecker {
        @Override
        public InputStream openInputStream () {
            toBeFile();
            return new ByteArrayInputStream(_data);
        }

        @Override
        public boolean exists () {
            return _content != null || _data != null;
        }

        @Override
        public boolean isDirectory () {
            return _content != null;
        }

        @Override
        public boolean isFile () {
            return _data != null;
        }

        @Override
        protected FileChecker descend (With parent, String name) {
            toBeFolder();

            Object obj = _content.get(name);
            byte[] data = (obj instanceof byte[]) ? ((byte[]) obj) : null;
            Map<String, Object> content = (obj instanceof Map) ? (Map) obj : null;

            return new ZipFileChecker(parent, name, data, content);
        }

        @Override
        protected void scan (Set<String> files, Set<String> folders) {
            toBeFolder();

            for (Map.Entry<String,Object> ent : _content.entrySet()) {
                Object val = ent.getValue();
                String name = ent.getKey();

                if (val instanceof Map) {
                    folders.add(name);
                } else {
                    files.add(name);
                }
            }
        }
        
        protected ZipFileChecker (With parent, String name, byte[] data, 
                                  Map<String, Object> content) {
            super(parent, name);

            _data = data;
            _content = content;
        }

        protected ZipFileChecker (With parent, String name) {
            super(parent, name);

            _logger.debug("Checking zip file: {}", getName());
            _content = ZipUtil.read(getName(), parent.file(name).openInputStream(), true);
            _data = null;
        }

        private final Map<String, Object> _content;
        private final byte[] _data;
    }

    /**
     * For example:
     * 
     *      expect(repo.getPackagesDir()).toBeFolder().withOnly().
     *          file("catalog.json").
     *              inspect(new ObjectInspector<Model.Catalog.Data>(Model.Catalog.Data.class) {
     *                  @Override public void inspect (Model.Catalog.Data data) {
     *                      Model.Catalog catalog = new Model.Catalog(data);
     *                      //System.out.println("Catalog: " + catalog);
     *                  }
     *              }).
     *          and().
     *          folder("mypkg").withOnly().
     *              file("catalog.json").
     *                  inspect(new ObjectInspector<Model.Catalog.Data>(Model.Catalog.Data.class) {
     *                      @Override public void inspect (Model.Catalog.Data data) {
     *                          Model.Catalog catalog = new Model.Catalog(data);
     *                          //System.out.println("mypkg catalog: " + catalog);
     *                      }
     *                  }).
     *              and().
     *              folder("*******").withOnly().
     *                  zip("mypkg.pkg").with().
     *                      folder(".sencha").withOnly().
     *                          file("package.json").inspect(pkgInspector).
     *                          and().
     *                          file("cert.json").
     *                          end().  // .sencha
     *                      end().  // mypkg.pkg
     *                  and().
     *                  file("package.json").
     *                      inspect(new ObjectInspector<Model.Package.Data>(Model.Package.Data.class) {
     *                          @Override public void inspect (Model.Package.Data pkgData) {
     *                              Model.Package pkg = new Model.Package(pkgData);
     *                              //System.out.println("Package: " + pkg);
     *                          }
     *                      }).
     *                  end(). // checks matching with or withOnly (folder *******)
     *              end().  // checks matching with or withOnly (folder mypkg)
     *          end()  // checks matching with or withOnly (pkgs)
     *      ;
     */
    public FileChecker expect (File file) {
        return new FileChecker(file);
    }

    //-------------------------------------------------------------------------

    @AfterClass
    public static void cleanupWebServer () {
        if (_webServer != null) {
            System.out.println("**************************************************");
            System.out.println("Stopping Jetty");
            Server server = _webServer;
            _webServer = null;

            if (server.isStarted()) {
                try {
                    server.stop();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
            System.out.println("**************************************************");
        }
    }

    protected static URL getWebRoot (File localPath) {
        String key = PathUtil.getCanonicalPath(localPath);
        URL url;

        synchronized (_webRoots) {
            url = _webRoots.get(key);

            if (url == null) {
                String contextPath = "/dir" + _webRoots.size();
                String urlText = "http://localhost:" + _port + contextPath + "/";

                try {
                    url = new URL(urlText);
                } catch (MalformedURLException ex) {
                    fail("Bad URL: " + urlText);
                }
                _webRoots.put(key, url);

                WebAppContext context = new WebAppContext();
                context.setContextPath(contextPath);
                // Turn off MMF use on Windows or else files requested get "locked" and
                // cannot be deleted
                context.setInitParameter("org.eclipse.jetty.servlet.Default.useFileMappedBuffer", "false");

                try {
                    context.setBaseResource(Resource.newResource(localPath));
                } catch (Exception ex) {
                    fail(ex.getMessage());
                }

                if (_contexts == null) {
                    _contexts = new ContextHandlerCollection();
                }
                _contexts.addHandler(context);

                if (_webServer == null) {
                    _webServer = new Server(_port);
                    _webServer.setHandler(_contexts);

                    System.out.println("**************************************************");
                    System.out.println("Starting Jetty");
                    try {
                        _webServer.start();
                    } catch (Exception ex) {
                        fail(ex.getMessage());
                    }
                    System.out.println("**************************************************");
                }
            }
        }

        return url;
    }

    //-------------------------------------------------------------------------

    public String compareTree (String sourcePath, String targetPath) {
        return compareTree(new File(sourcePath), new File(targetPath));
    }

    public String compareTree (File sourceFile, File targetFile) {
        String[] sourceList = sourceFile.list();
        String[] targetList = targetFile.list();

        Arrays.sort(sourceList);
        Arrays.sort(targetList);

        _logger.debug("checking file counts for {} and {}",
                      sourceFile.getAbsolutePath(), targetFile.getAbsolutePath());

        assertEquals(toJson(sourceList, true), toJson(targetList, true));
        assertEquals(sourceList.length, targetList.length);

        if (sourceList.length < targetList.length) {
            return targetList[sourceList.length];
        }

        if (targetList.length < sourceList.length) {
            return sourceList[targetList.length];
        }

        for (int i = 0; i < sourceList.length; i++) {
            assertEquals(sourceList[i], targetList[i]);

            if (!sourceList[i].equals(targetList[i])) {
                return sourceList[i];
            }

            File sourceChildFile = new File(sourceFile, sourceList[i]);
            File targetChildFile = new File(targetFile, targetList[i]);

            assertEquals(sourceChildFile.isDirectory(), targetChildFile.isDirectory());

            if (!sourceChildFile.isDirectory()) {
                compareFile(sourceChildFile, targetChildFile);
            } else if (sourceChildFile.isDirectory()) {
                compareTree(sourceChildFile, targetChildFile);
            } else {
                _logger.debug("Skipping {}", sourceChildFile.getAbsolutePath());
            }
        }
        return null;
    }

    private void compareFile(File sourceChildFile, File targetChildFile) {
        String srcData = FileUtil.readFile(sourceChildFile),
               tstData = FileUtil.readFile(targetChildFile);
        String expected = removeDuplicateLineEndings(srcData);
        String actual = removeDuplicateLineEndings(tstData);

        if(!sourceChildFile.getAbsolutePath().endsWith("codegen.json") ||
           _compareCodegenJson) {
            if (!expected.equals(actual)) {
                System.out.println("Comparing " +
                    escapeForLogging(sourceChildFile.getAbsolutePath()) + " to " +
                    escapeForLogging(targetChildFile.getAbsolutePath()));

                assertEquals(expected, actual);
            }
        }
    }

    //-------------------------------------------------------------------------

    private static Server _webServer;
    private static final Map<String, URL> _webRoots = new HashMap();
    private static ContextHandlerCollection _contexts;
    private static final int _port = 1842;
    
    protected boolean _compareCodegenJson = true;
    
    private static final Logger _logger = SenchaLogManager.getLogger();
}
