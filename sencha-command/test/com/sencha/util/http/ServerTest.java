package com.sencha.util.http;

import java.io.File;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Test;

import com.sencha.util.StreamUtil;

public class ServerTest {
    
    public static class MyResponder extends BasicTextResponder {
        public String handleGet() {
            return "My responder is alive!";
        }
    };
    
    @Test
    public void respondersCanBeMountedIntoContexts() throws Exception {
        Server server = new Server(1981);
        server.mount("/my-responder", new MyResponder());
        server.start();

        String response = httpGet("http://localhost:1981/my-responder/");
        Assert.assertEquals("My responder is alive!", response);
        
        server.stop();
    }
    
    @Test
    public void directoriesCanBeMountedIntoContexts() throws Exception {
        File directory = new File(FileUtils.getTempDirectory(), "temp" + System.currentTimeMillis());
        directory.mkdir();
        FileUtils.writeStringToFile(new File(directory, "foo.txt"), "I'm foo!");
        
        Server server = new Server(1981);
        server.mount("/my-files", directory);
        server.start();
        
        String response = httpGet("http://localhost:1981/my-files/foo.txt");
        Assert.assertEquals("I'm foo!", response);
        
        server.stop();
        FileUtils.deleteDirectory(directory);
    }
    
    @Test
    public void directoriesAndResponderCanCoexistAsContexts() throws Exception {
        File directory = new File(FileUtils.getTempDirectory(), "temp" + System.currentTimeMillis());
        directory.mkdir();
        FileUtils.writeStringToFile(new File(directory, "foo.txt"), "I'm foo!");
        
        Server server = new Server(1981);
        server.mount("/", new MyResponder());
        server.mount("/my-files", directory);
        server.start();

        String responderResponse = httpGet("http://localhost:1981/");
        Assert.assertEquals("My responder is alive!", responderResponse);
        
        String fileResponse = httpGet("http://localhost:1981/my-files/foo.txt");
        Assert.assertEquals("I'm foo!", fileResponse);
        
        server.stop();
        FileUtils.deleteDirectory(directory);
    }
    
    private String httpGet(String urlSpec) throws Exception {
        URL url = new URL(urlSpec);
        HttpURLConnection http = (HttpURLConnection) url.openConnection();
        http.connect();
        InputStream input = http.getInputStream(); // throw if not 200 OK
        return StreamUtil.readAllAndClose(input);
    }

}
