package com.sencha.util;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.junit.Assert;
import org.junit.Test;

public class ZipUtilTest {
    
    @Test
    public void createsZipArchivesFromAListOfZippingDescriptors() throws Exception {
        File workDirectory = new File(FileUtils.getTempDirectory(), "temp" + System.currentTimeMillis()); workDirectory.mkdir();
        
        File dirApple   = new File(workDirectory, "apple");  dirApple.mkdir();
        File dirBanana  = new File(workDirectory, "banana"); dirBanana.mkdir();
        File dirWater   = new File(workDirectory, "water");  dirWater.mkdir();
        File fileApple  = new File(dirApple,  "name.txt");   FileUtils.writeStringToFile(fileApple,  "Apple");
        File fileBanana = new File(dirBanana, "name.txt");   FileUtils.writeStringToFile(fileBanana, "Banana");
        File fileWater  = new File(dirWater,  "name.txt");   FileUtils.writeStringToFile(fileWater,  "Water");
        
        
        List<ZipUtil.Source> zipSources = Arrays.asList(
                // apple/name.txt  -> contents/fruits/apple/name.txt
                new ZipUtil.Source(fileApple,  workDirectory, "contents/fruits"),
                // banana/name.txt -> contents/fruits/banana/name.txt
                new ZipUtil.Source(fileBanana, workDirectory, "contents/fruits"),
                // water/name.txt  -> contents/drinks/water/name.txt
                new ZipUtil.Source(fileWater,  workDirectory, "contents/drinks")
        );
        
        File zipArchive = new File(workDirectory, "output.zip");
        ZipUtil.zip(new FileOutputStream(zipArchive), zipSources);
        
        System.out.println(zipArchive.getAbsolutePath());
        ZipUtil.unzip(zipArchive, workDirectory);
        
        // contents
        File dirContents = new File(workDirectory, "contents");
        Assert.assertTrue(dirContents.exists());
        Assert.assertTrue(dirContents.isDirectory());
        // contents/fruits
        File dirFruits = new File(dirContents, "fruits");
        Assert.assertTrue(dirFruits.exists());
        Assert.assertTrue(dirContents.exists());
        // contents/drinks
        File dirDrinks = new File(dirContents, "drinks");
        Assert.assertTrue(dirDrinks.exists());
        Assert.assertTrue(dirDrinks.isDirectory());
        // contents/fruits/apple
        File newDirApple = new File(dirFruits, "apple");
        Assert.assertTrue(newDirApple.exists());
        Assert.assertTrue(newDirApple.isDirectory());
        // contents/fruits/banana
        File newDirBanana = new File(dirFruits, "banana");
        Assert.assertTrue(newDirBanana.exists());
        Assert.assertTrue(newDirBanana.isDirectory());
        // contents/drinks/water
        File newDirWater = new File(dirDrinks, "water");
        Assert.assertTrue(newDirWater.exists());
        Assert.assertTrue(newDirWater.isDirectory());
        
        // contents/fruits/apple/name.txt
        Assert.assertEquals("Apple",  FileUtils.readFileToString(new File(newDirApple,  "name.txt")));
        // contents/fruits/banana/name.txt
        Assert.assertEquals("Banana", FileUtils.readFileToString(new File(newDirBanana, "name.txt")));
        // contents/drinks/water/name.txt
        Assert.assertEquals("Water",  FileUtils.readFileToString(new File(newDirWater,  "name.txt")));
    }

}
