/*
 * Copyright (c) 2012. Sencha Inc.
 */
package com.sencha.tools.generator;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.junit.Test;

import com.sencha.util.FileUtil;

import static com.sencha.util.StringUtil.normalizeContent;
import static com.sencha.util.StringUtil.normalizeLineEndings;
import static com.sencha.tools.generator.Generator.*;
import static org.junit.Assert.*;

public class GeneratorTest extends GeneratorTestBase {
    public GeneratorTest() {
    }

    @Test
    public void templateWithDescriptor () {
        Generator generator = createGenerator("basic");
        //System.out.println("Source: " + generator.getSource());
        System.out.println("Target: " + generator.getTarget());

        //Descriptor descriptor = generator.getDescriptor();

        generator.getContext().put("userView", "MainView");
        generator.generate();

        compareTree(getExpectedFileDir("basic"), generator.getTarget());
    }

    @Test
    public void testApacheVelocity() {
        FileUtil.writeFile(getFile("source/f1" + VELOCITY_SUFFIX), getContent("input/ModelTest.vm"));
        Generator generator = createGenerator(true);

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(getContent("output/ModelTest.vm"),
                                FileUtil.readFile(getFile("target/f1")));
    }

    @Test
    public void testApacheVelocityDefault() {
        FileUtil.writeFile(getFile("source/f1" + VELOCITY_SUFFIX + SACRED_SUFFIX), getContent("input/ModelTest.vm"));
        Generator generator = createGenerator(true);

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(getContent("output/ModelTest.vm"),
                                FileUtil.readFile(getFile("target/f1")));
        FileUtil.writeFile(getFile("target/f1"), "This is a user change");
        generator.generate();
        assertFileContentEquals("This is a user change",
                                FileUtil.readFile(getFile("target/f1")));
    }

    @Test
    public void testApacheVelocitySeriallyReusable() {
        FileUtil.writeFile(getFile("source/f1" + VELOCITY_SUFFIX), ">${name}<");
        Generator generator = createGenerator(false);
        Map<String, Object> map = generator.getContext();
        map.put("name", "first");

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(">first<", FileUtil.readFile(getFile("target/f1")));
        map.clear();
        map.put("name", "second");
        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(">second<", FileUtil.readFile(getFile("target/f1")));
    }

    @Test
    public void testArbitraryTree() {
        FileUtil.writeFile(getFile("source/f1" + VELOCITY_SUFFIX), getContent("input/ModelTest.vm"));
        FileUtil.writeFile(getFile("source/f2" + XTEMPLATE_SUFFIX), getContent("input/ModelTest.tpl"));
        FileUtil.writeFile(getFile("source/d1/f1" + VELOCITY_SUFFIX), getContent("input/ModelTest.vm"));
        FileUtil.writeFile(getFile("source/d1/f2" + XTEMPLATE_SUFFIX), getContent("input/ModelTest.tpl"));
        FileUtil.createDirectory(getFilePath("source/d1/d1"));

        Generator generator = createGenerator(true);

        generator.generate();

        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(getContent("output/ModelTest.vm"),
                                FileUtil.readFile(getFile("target/f1")));

        assertTrue(getFile("target/f2").exists());
        assertFileContentEquals(getContent("output/ModelTest.tpl"),
                                FileUtil.readFile(getFile("target/f2")));

        assertTrue(getFile("target/d1/f1").exists());
        assertFileContentEquals(getContent("output/ModelTest.vm"),
                                FileUtil.readFile(getFile("target/f1")));

        assertTrue(getFile("target/d1/f2").exists());
        assertFileContentEquals(getContent("output/ModelTest.tpl"),
                                FileUtil.readFile(getFile("target/f2")));

        assertTrue(getFile("target/d1/d1").isDirectory());
    }

    @Test
    public void testPlainFile() {
        FileUtil.writeFile(getFile("source/f1"), getContent("input/ModelTest.vm"));
        Generator generator = createGenerator(true);

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(getContent("input/ModelTest.vm"),
                                FileUtil.readFile(getFile("target/f1")));
    }

    @Test
    public void testSenchaTemplate() {
        FileUtil.writeFile(getFile("source/f1" + XTEMPLATE_SUFFIX), getContent("input/ModelTest.tpl"));
        Generator generator = createGenerator(true);

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(getContent("output/ModelTest.tpl"),
                                FileUtil.readFile(getFile("target/f1")));
    }

    @Test
    public void testSenchaTemplateDefault() {
        FileUtil.writeFile(getFile("source/f1" + XTEMPLATE_SUFFIX + SACRED_SUFFIX), 
                           getContent("input/ModelTest.tpl"));
        Generator generator = createGenerator(true);

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(getContent("output/ModelTest.tpl"),
                                FileUtil.readFile(getFile("target/f1")));
        FileUtil.writeFile(getFile("target/f1"), "This is a user change");
        generator.generate();
        assertFileContentEquals("This is a user change",
                                FileUtil.readFile(getFile("target/f1")));
    }

    @Test
    public void testSenchaTemplateSeriallyReusable() {
        FileUtil.writeFile(getFile("source/f1" + XTEMPLATE_SUFFIX), ">{name}<");

        Generator generator = createGenerator(false);
        Map<String, Object> map = generator.getContext();
        map.put("name", "first");

        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(">first<", FileUtil.readFile(getFile("target/f1")));
        map.clear();
        map.put("name", "second");
        generator.generate();
        assertTrue(getFile("target/f1").exists());
        assertFileContentEquals(">second<", FileUtil.readFile(getFile("target/f1")));
    }

    //-------------------------------------------------------------------------

    private static void assertFileContentEquals (String input, String test) {
        input = normalizeContent(input);
        test = normalizeContent(test);

        assertEquals(input, test);
    }

    private Generator createGenerator (File source, File target) {
        Generator generator = new Generator(source);

        generator.setTarget(target);

        return generator;
    }

    private Generator createGenerator (String name) {
        return createGenerator(getTestFilesDir(name + "/source"),
                               getTestTempDir(name));
    }

    private Generator createGenerator (boolean populateContext) {
        Generator generator = createGenerator(getFile("source"), getFile("target"));

        if (populateContext) {
            Map<String, Object> context = generator.getContext();

            context.put("appName", "SubstitutedAppName");
            context.put("name", "SubstitutedName");

            // It would be nice to use _base literal syntax here:
            //
            //  new HashMap<String,String>() {{
            //      put("name", "name01");
            //      put("type", "type01");
            //  }}
            //
            // but that trips up Gson and the JS engine! The debugger shows some sort of
            // anonymous type involved for the actual maps, and that sends things into a
            // tail spin...
            //
            context.put("fields", new Object[] {
                createMap("name", "name01",
                          "type", "type01"),
                createMap("name", "name02",
                          "type", "type02"),
                createMap("name", "name03",
                          "type", "type03")
            });
        }

        return generator;
    }
    
    private Map<String, String> createMap (String... pairs) {
        Map<String, String> map = new HashMap<String, String>();
        
        for (int i = 0; i < pairs.length; i += 2) {
            map.put(pairs[i], pairs[i+1]);
        }

        return map;
    }

    private String getContent (String name) {
        return FileUtil.readFile(getResourcePath(name));
    }

    private File getExpectedFileDir (String name) {
        return getTestFilesDir(name + "/expected");
    }
}
