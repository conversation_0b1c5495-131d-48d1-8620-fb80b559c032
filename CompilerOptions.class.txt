//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.google.javascript.jscomp;

import com.google.javascript.jscomp.SourceMap.DetailLevel;
import com.google.javascript.jscomp.SourceMap.Format;
import com.google.javascript.jscomp.annotations.LegacySetFeatureSetCaller;
import com.google.javascript.jscomp.base.Tri;
import com.google.javascript.jscomp.deps.ModuleLoader;
import com.google.javascript.jscomp.deps.ModuleLoader.PathEscaper;
import com.google.javascript.jscomp.deps.ModuleLoader.ResolutionMode;
import com.google.javascript.jscomp.jarjar.com.google.common.annotations.GwtIncompatible;
import com.google.javascript.jscomp.jarjar.com.google.common.annotations.VisibleForTesting;
import com.google.javascript.jscomp.jarjar.com.google.common.base.Ascii;
import com.google.javascript.jscomp.jarjar.com.google.common.base.MoreObjects;
import com.google.javascript.jscomp.jarjar.com.google.common.base.Optional;
import com.google.javascript.jscomp.jarjar.com.google.common.base.Preconditions;
import com.google.javascript.jscomp.jarjar.com.google.common.base.Strings;
import com.google.javascript.jscomp.jarjar.com.google.common.collect.ImmutableList;
import com.google.javascript.jscomp.jarjar.com.google.common.collect.ImmutableMap;
import com.google.javascript.jscomp.jarjar.com.google.common.collect.ImmutableSet;
import com.google.javascript.jscomp.jarjar.com.google.common.collect.LinkedHashMultimap;
import com.google.javascript.jscomp.jarjar.com.google.common.collect.Multimap;
import com.google.javascript.jscomp.jarjar.com.google.common.primitives.Chars;
import com.google.javascript.jscomp.jarjar.com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.google.javascript.jscomp.jarjar.com.google.errorprone.annotations.InlineMe;
import com.google.javascript.jscomp.jarjar.com.google.errorprone.annotations.RestrictedApi;
import com.google.javascript.jscomp.parsing.Config;
import com.google.javascript.jscomp.parsing.Config.JsDocParsing;
import com.google.javascript.jscomp.parsing.parser.FeatureSet;
import com.google.javascript.jscomp.resources.ResourceLoader;
import com.google.javascript.rhino.IR;
import com.google.javascript.rhino.Node;
import com.google.javascript.rhino.SourcePosition;
import java.io.IOException;
import java.io.InputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.nio.charset.Charset;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import org.jspecify.nullness.Nullable;

public class CompilerOptions implements Serializable {
    static final int DEFAULT_LINE_LENGTH_THRESHOLD = 500;
    private static final char[] POLYMER_PROPERTY_RESERVED_FIRST_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ$".toCharArray();
    private static final char[] POLYMER_PROPERTY_RESERVED_NON_FIRST_CHARS = "_$".toCharArray();
    private static final char[] ANGULAR_PROPERTY_RESERVED_FIRST_CHARS = new char[]{'$'};
    private Optional<Boolean> emitUseStrict = Optional.absent();
    private LanguageMode languageIn;
    private Optional<FeatureSet> outputFeatureSet = Optional.absent();
    private ImmutableList<ExperimentalForceTranspile> experimentalForceTranspiles = ImmutableList.of();
    private Optional<Boolean> languageOutIsDefaultStrict = Optional.absent();
    private Environment environment;
    private BrowserFeaturesetYear browserFeaturesetYear;
    private boolean instrumentForCoverageOnly = false;
    private @Nullable Path typedAstOutputFile = null;
    private boolean mergedPrecompiledLibraries = false;
    boolean inferConsts = true;
    private boolean assumeStrictThis;
    private boolean preserveDetailedSourceInfo = false;
    private boolean preserveNonJSDocComments = false;
    private boolean continueAfterErrors = false;
    private IncrementalCheckMode incrementalCheckMode;
    private Config.JsDocParsing parseJsDocDocumentation;
    private boolean printExterns;
    boolean inferTypes;
    boolean skipNonTranspilationPasses;
    DevMode devMode;
    private boolean checkDeterminism;
    private DependencyOptions dependencyOptions;
    public @Nullable MessageBundle messageBundle;
    private boolean strictMessageReplacement;
    public boolean checkSymbols;
    public boolean checkSuspiciousCode;
    public boolean checkTypes;
    @Nullable Set<String> extraAnnotationNames;
    int numParallelThreads;
    public boolean foldConstants;
    public boolean deadAssignmentElimination;
    public boolean inlineConstantVars;
    int maxFunctionSizeAfterInlining;
    static final int UNLIMITED_FUN_SIZE_AFTER_INLINING = -1;
    boolean assumeClosuresOnlyCaptureReferences;
    private boolean inlineProperties;
    private boolean crossChunkCodeMotion;
    boolean crossChunkCodeMotionNoStubMethods;
    boolean parentChunkCanSeeSymbolsDeclaredInChildren;
    public boolean coalesceVariableNames;
    private boolean crossChunkMethodMotion;
    boolean inlineGetters;
    public boolean inlineVariables;
    boolean inlineLocalVariables;
    public boolean flowSensitiveInlineVariables;
    public boolean smartNameRemoval;
    public boolean removeUnreachableCode;
    ExtractPrototypeMemberDeclarationsMode extractPrototypeMemberDeclarations;
    public boolean removeUnusedPrototypeProperties;
    public boolean removeUnusedClassProperties;
    boolean removeUnusedConstructorProperties;
    public boolean removeUnusedVars;
    public boolean removeUnusedLocalVars;
    public boolean collapseVariableDeclarations;
    public boolean collapseAnonymousFunctions;
    private AliasStringsMode aliasStringsMode;
    boolean outputJsStringUsage;
    public boolean convertToDottedProperties;
    public boolean rewriteFunctionExpressions;
    public boolean optimizeCalls;
    boolean optimizeESClassConstructors;
    public boolean optimizeArgumentsArray;
    boolean useTypesForLocalOptimization;
    boolean useSizeHeuristicToStopOptimizationLoop;
    int optimizationLoopMaxIterations;
    public VariableRenamingPolicy variableRenaming;
    PropertyRenamingPolicy propertyRenaming;
    private boolean propertyRenamingOnlyCompilationMode;
    public boolean labelRenaming;
    public boolean reserveRawExports;
    boolean preferStableNames;
    public boolean generatePseudoNames;
    public @Nullable String renamePrefix;
    public String renamePrefixNamespace;
    boolean renamePrefixNamespaceAssumeCrossChunkNames;
    private PropertyCollapseLevel collapsePropertiesLevel;
    boolean collapseObjectLiterals;
    public boolean devirtualizeMethods;
    public boolean computeFunctionSideEffects;
    private boolean disambiguateProperties;
    private boolean ambiguateProperties;
    ImmutableMap<String, SourceMapInput> inputSourceMaps;
    VariableMap inputVariableMap;
    VariableMap inputPropertyMap;
    public boolean exportTestFunctions;
    NameGenerator nameGenerator;
    private boolean replaceMessagesWithChromeI18n;
    String tcProjectId;
    private CodingConvention codingConvention;
    public @Nullable String syntheticBlockStartMarker;
    public @Nullable String syntheticBlockEndMarker;
    public @Nullable String locale;
    private boolean doLateLocalization;
    public boolean markAsCompiled;
    public boolean closurePass;
    private boolean preserveClosurePrimitives;
    boolean angularPass;
    @Nullable Integer polymerVersion;
    PolymerExportPolicy polymerExportPolicy;
    private boolean chromePass;
    J2clPassMode j2clPassMode;
    boolean j2clMinifierEnabled;
    @Nullable String j2clMinifierPruningManifest;
    boolean removeAbstractMethods;
    boolean removeClosureAsserts;
    boolean removeJ2clAsserts;
    public boolean gatherCssNames;
    ImmutableSet<String> stripTypes;
    ImmutableSet<String> stripNameSuffixes;
    ImmutableSet<String> stripNamePrefixes;
    protected transient @Nullable Multimap<CustomPassExecutionTime, CompilerPass> customPasses;
    private final LinkedHashMap<String, Object> defineReplacements;
    private TweakProcessing tweakProcessing;
    public boolean rewriteGlobalDeclarationsForTryCatchWrapping;
    boolean checksOnly;
    OutputJs outputJs;
    public boolean generateExports;
    boolean exportLocalPropertyDefinitions;
    public @Nullable CssRenamingMap cssRenamingMap;
    @Nullable Set<String> cssRenamingSkiplist;
    boolean replaceIdGenerators;
    ImmutableMap<String, RenamingMap> idGenerators;
    Xid.HashFunction xidHashFunction;
    Xid.HashFunction chunkIdHashFunction;
    String idGeneratorsMapSerialized;
    List<String> replaceStringsFunctionDescriptions;
    String replaceStringsPlaceholderToken;
    VariableMap replaceStringsInputMap;
    private ImmutableSet<String> propertiesThatMustDisambiguate;
    private boolean processCommonJSModules;
    List<String> moduleRoots;
    boolean rewritePolyfills;
    private boolean isolatePolyfills;
    List<String> forceLibraryInjection;
    boolean preventLibraryInjection;
    boolean assumeForwardDeclaredForMissingTypes;
    @Nullable ImmutableSet<String> unusedImportsToRemove;
    public boolean preserveTypeAnnotations;
    public boolean gentsMode;
    private boolean prettyPrint;
    public boolean lineBreak;
    public boolean printInputDelimiter;
    public String inputDelimiter;
    private @Nullable Path debugLogDirectory;
    private String debugLogFilter;
    private boolean serializeExtraDebugInfo;
    private boolean quoteKeywordProperties;
    boolean preferSingleQuotes;
    boolean trustedStrings;
    boolean printSourceAfterEachPass;
    List<String> filesToPrintAfterEachPassRegexList;
    List<String> chunksToPrintAfterEachPassRegexList;
    List<String> qnameUsesToPrintAfterEachPassList;
    private TracerMode tracer;
    private Path tracerOutput;
    private boolean colorizeErrorOutput;
    public ErrorFormat errorFormat;
    private ComposeWarningsGuard warningsGuard;
    int summaryDetailLevel;
    int lineLengthThreshold;
    boolean useOriginalNamesInOutput;
    private @Nullable String externExportsPath;
    private final List<SortingErrorManager.ErrorReportGenerator> extraReportGenerators;
    private String sourceMapOutputPath;
    private boolean shouldAlwaysGatherSourceMapInfo;
    public SourceMap.DetailLevel sourceMapDetailLevel;
    public SourceMap.Format sourceMapFormat;
    boolean parseInlineSourceMaps;
    boolean applyInputSourceMaps;
    boolean resolveSourceMapAnnotations;
    public List<? extends SourceMap.LocationMapping> sourceMapLocationMappings;
    boolean sourceMapIncludeSourcesContent;
    transient Charset outputCharset;
    private boolean protectHiddenSideEffects;
    private boolean assumeGettersArePure;
    private boolean assumeStaticInheritanceIsNotUsed;
    private transient AliasTransformationHandler aliasHandler;
    transient @Nullable ErrorHandler errorHandler;
    private InstrumentOption instrumentForCoverageOption;
    private String productionInstrumentationArrayName;
    private static final ImmutableList<ConformanceConfig> GLOBAL_CONFORMANCE_CONFIGS = ImmutableList.of(ResourceLoader.loadGlobalConformance(CompilerOptions.class));
    private ImmutableList<ConformanceConfig> conformanceConfigs;
    private Optional<Pattern> conformanceRemoveRegexFromPath;
    boolean wrapGoogModulesForWhitespaceOnly;
    boolean printConfig;
    private Optional<Boolean> isStrictModeInput;
    private boolean rewriteModulesBeforeTypechecking;
    private boolean enableModuleRewriting;
    ModuleLoader.ResolutionMode moduleResolutionMode;
    private ImmutableMap<String, String> browserResolverPrefixReplacements;
    private ModuleLoader.PathEscaper pathEscaper;
    List<String> packageJsonEntryNames;
    private boolean allowDynamicImport;
    private @Nullable String dynamicImportAlias;
    ChunkOutputType chunkOutputType;
    private Reach inlineFunctionsLevel;
    private boolean allowZoneJsWithAsyncFunctionsInOutput;
    private Es6ModuleTranspilation es6ModuleTranspilation;
    static final AliasTransformationHandler NULL_ALIAS_TRANSFORMATION_HANDLER = new NullAliasTransformationHandler();

    public static ImmutableSet<Character> getAngularPropertyReservedFirstChars() {
        return ImmutableSet.copyOf(Chars.asList(ANGULAR_PROPERTY_RESERVED_FIRST_CHARS));
    }

    public boolean shouldRunCrossChunkCodeMotion() {
        return this.crossChunkCodeMotion;
    }

    public boolean shouldRunCrossChunkMethodMotion() {
        return this.crossChunkMethodMotion;
    }

    public String getSourceMapOutputPath() {
        return this.sourceMapOutputPath;
    }

    public boolean shouldGatherSourceMapInfo() {
        return this.shouldAlwaysGatherSourceMapInfo || !Strings.isNullOrEmpty(this.sourceMapOutputPath);
    }

    public void setAlwaysGatherSourceMapInfo(boolean shouldAlwaysGatherSourceMapInfo) {
        this.shouldAlwaysGatherSourceMapInfo = shouldAlwaysGatherSourceMapInfo;
    }

    public int getBrowserFeaturesetYear() {
        return this.browserFeaturesetYear != null ? this.browserFeaturesetYear.year : 0;
    }

    public void setBrowserFeaturesetYear(int year) {
        this.browserFeaturesetYear = new BrowserFeaturesetYear(year);
        this.browserFeaturesetYear.setDependentValuesFromYear();
    }

    public void setInstrumentForCoverageOnly(boolean instrumentForCoverageOnly) {
        this.instrumentForCoverageOnly = instrumentForCoverageOnly;
    }

    public boolean getInstrumentForCoverageOnly() {
        return this.instrumentForCoverageOnly;
    }

    public void setTypedAstOutputFile(@Nullable Path file) {
        this.typedAstOutputFile = file;
    }

    @Nullable Path getTypedAstOutputFile() {
        return this.typedAstOutputFile;
    }

    void setMergedPrecompiledLibraries(boolean mergedPrecompiledLibraries) {
        this.mergedPrecompiledLibraries = mergedPrecompiledLibraries;
    }

    public boolean getMergedPrecompiledLibraries() {
        return this.mergedPrecompiledLibraries;
    }

    /** @deprecated */
    @Deprecated
    public void setSkipTranspilationAndCrash(boolean value) {
    }

    public void setInputSourceMaps(final ImmutableMap<String, SourceMapInput> inputSourceMaps) {
        this.inputSourceMaps = inputSourceMaps;
    }

    public void setInferConst(boolean value) {
        this.inferConsts = value;
    }

    public void setIncrementalChecks(IncrementalCheckMode value) {
        this.incrementalCheckMode = value;
        switch (value) {
            case GENERATE_IJS:
                this.setPreserveTypeAnnotations(true);
                this.setOutputJs(CompilerOptions.OutputJs.NORMAL);
            case OFF:
            case RUN_IJS_CHECKS_LATE:
            default:
        }
    }

    public boolean shouldGenerateTypedExterns() {
        return this.incrementalCheckMode == CompilerOptions.IncrementalCheckMode.GENERATE_IJS;
    }

    public boolean shouldRunTypeSummaryChecksLate() {
        return this.incrementalCheckMode == CompilerOptions.IncrementalCheckMode.RUN_IJS_CHECKS_LATE;
    }

    void setPrintExterns(boolean printExterns) {
        this.printExterns = printExterns;
    }

    boolean shouldPrintExterns() {
        return this.printExterns || this.incrementalCheckMode == CompilerOptions.IncrementalCheckMode.GENERATE_IJS;
    }

    /** @deprecated */
    @Deprecated
    public void setCheckGlobalThisLevel(CheckLevel level) {
    }

    public void setNumParallelThreads(int parallelism) {
        this.numParallelThreads = parallelism;
    }

    @VisibleForTesting
    public void setRenamePrefixNamespaceAssumeCrossChunkNames(boolean assume) {
        this.renamePrefixNamespaceAssumeCrossChunkNames = assume;
    }

    /** @deprecated */
    @Deprecated
    public boolean shouldCollapseProperties() {
        return this.collapsePropertiesLevel != CompilerOptions.PropertyCollapseLevel.NONE;
    }

    public PropertyCollapseLevel getPropertyCollapseLevel() {
        return this.collapsePropertiesLevel;
    }

    public void setCollapseObjectLiterals(boolean enabled) {
        this.collapseObjectLiterals = enabled;
    }

    public boolean getCollapseObjectLiterals() {
        return this.collapseObjectLiterals;
    }

    public void setNameGenerator(NameGenerator nameGenerator) {
        this.nameGenerator = nameGenerator;
    }

    public void setReplaceMessagesWithChromeI18n(boolean replaceMessagesWithChromeI18n, String tcProjectId) {
        if (replaceMessagesWithChromeI18n && this.messageBundle != null && !(this.messageBundle instanceof EmptyMessageBundle)) {
            throw new RuntimeException("When replacing messages with chrome.i18n.getMessage, a message bundle should not be specified.");
        } else {
            this.replaceMessagesWithChromeI18n = replaceMessagesWithChromeI18n;
            this.tcProjectId = tcProjectId;
        }
    }

    public boolean shouldRunReplaceMessagesForChrome() {
        if (!this.replaceMessagesWithChromeI18n) {
            return false;
        } else {
            Preconditions.checkState(this.messageBundle == null || this.messageBundle instanceof EmptyMessageBundle, "When replacing messages with chrome.i18n.getMessage, a message bundle should not be specified.");
            Preconditions.checkState(!this.doLateLocalization, "Late localization is not supported for chrome.i18n.getMessage");
            return true;
        }
    }

    public void setAssumeForwardDeclaredForMissingTypes(boolean assumeForwardDeclaredForMissingTypes) {
        this.assumeForwardDeclaredForMissingTypes = assumeForwardDeclaredForMissingTypes;
    }

    public void setPreferSingleQuotes(boolean enabled) {
        this.preferSingleQuotes = enabled;
    }

    public void setTrustedStrings(boolean yes) {
        this.trustedStrings = yes;
    }

    public void setPrintSourceAfterEachPass(boolean printSource) {
        this.printSourceAfterEachPass = printSource;
    }

    public void setFilesToPrintAfterEachPassRegexList(List<String> filePathRegexList) {
        this.filesToPrintAfterEachPassRegexList = filePathRegexList;
    }

    public void setChunksToPrintAfterEachPassRegexList(List<String> chunkPathRegexList) {
        this.chunksToPrintAfterEachPassRegexList = chunkPathRegexList;
    }

    public void setQnameUsesToPrintAfterEachPassList(List<String> qnameRegexList) {
        this.qnameUsesToPrintAfterEachPassList = qnameRegexList;
    }

    public TracerMode getTracerMode() {
        return this.tracer;
    }

    public void setTracerMode(TracerMode mode) {
        this.tracer = mode;
    }

    Path getTracerOutput() {
        return this.tracerOutput;
    }

    public void setTracerOutput(Path out) {
        this.tracerOutput = out;
    }

    List<SortingErrorManager.ErrorReportGenerator> getExtraReportGenerators() {
        return this.extraReportGenerators;
    }

    void addReportGenerator(SortingErrorManager.ErrorReportGenerator generator) {
        this.extraReportGenerators.add(generator);
    }

    public void setProtectHiddenSideEffects(boolean enable) {
        this.protectHiddenSideEffects = enable;
    }

    public boolean shouldProtectHiddenSideEffects() {
        return this.protectHiddenSideEffects && (!this.checksOnly || this.getTypedAstOutputFile() != null);
    }

    public void setAssumeGettersArePure(boolean x) {
        this.assumeGettersArePure = x;
    }

    public boolean getAssumeGettersArePure() {
        return this.assumeGettersArePure;
    }

    public void setAssumeStaticInheritanceIsNotUsed(boolean x) {
        this.assumeStaticInheritanceIsNotUsed = x;
    }

    public boolean getAssumeStaticInheritanceIsNotUsed() {
        return this.assumeStaticInheritanceIsNotUsed;
    }

    public void setConformanceRemoveRegexFromPath(Optional<Pattern> pattern) {
        this.conformanceRemoveRegexFromPath = pattern;
    }

    public Optional<Pattern> getConformanceRemoveRegexFromPath() {
        return this.conformanceRemoveRegexFromPath;
    }

    public void setWrapGoogModulesForWhitespaceOnly(boolean enable) {
        this.wrapGoogModulesForWhitespaceOnly = enable;
    }

    public void setBadRewriteModulesBeforeTypecheckingThatWeWantToGetRidOf(boolean b) {
        this.rewriteModulesBeforeTypechecking = b;
    }

    boolean shouldRewriteModulesBeforeTypechecking() {
        return this.enableModuleRewriting && (this.rewriteModulesBeforeTypechecking || this.processCommonJSModules);
    }

    public void setEnableModuleRewriting(boolean enable) {
        this.enableModuleRewriting = enable;
    }

    boolean shouldRewriteModulesAfterTypechecking() {
        return this.enableModuleRewriting && !this.rewriteModulesBeforeTypechecking;
    }

    boolean shouldRewriteModules() {
        return this.enableModuleRewriting;
    }

    public void setPrintConfig(boolean printConfig) {
        this.printConfig = printConfig;
    }

    public void setAllowDynamicImport(boolean value) {
        this.allowDynamicImport = value;
    }

    boolean shouldAllowDynamicImport() {
        return this.allowDynamicImport;
    }

    public String getDynamicImportAlias() {
        return this.dynamicImportAlias;
    }

    public void setDynamicImportAlias(String value) {
        this.dynamicImportAlias = value;
    }

    boolean shouldAliasDynamicImport() {
        return this.dynamicImportAlias != null;
    }

    public CompilerOptions() {
        this.incrementalCheckMode = CompilerOptions.IncrementalCheckMode.OFF;
        this.parseJsDocDocumentation = JsDocParsing.TYPES_ONLY;
        this.dependencyOptions = DependencyOptions.none();
        this.messageBundle = null;
        this.numParallelThreads = 1;
        this.useSizeHeuristicToStopOptimizationLoop = true;
        this.renamePrefixNamespaceAssumeCrossChunkNames = false;
        this.j2clMinifierEnabled = true;
        this.j2clMinifierPruningManifest = null;
        this.removeJ2clAsserts = true;
        this.replaceIdGenerators = true;
        this.processCommonJSModules = false;
        this.moduleRoots = ImmutableList.of("./");
        this.rewritePolyfills = false;
        this.isolatePolyfills = false;
        this.forceLibraryInjection = ImmutableList.of();
        this.preventLibraryInjection = false;
        this.assumeForwardDeclaredForMissingTypes = false;
        this.inputDelimiter = "// Input %num%";
        this.filesToPrintAfterEachPassRegexList = ImmutableList.of();
        this.chunksToPrintAfterEachPassRegexList = ImmutableList.of();
        this.qnameUsesToPrintAfterEachPassList = ImmutableList.of();
        this.warningsGuard = new ComposeWarningsGuard(new WarningsGuard[0]);
        this.summaryDetailLevel = 1;
        this.lineLengthThreshold = 500;
        this.useOriginalNamesInOutput = false;
        this.extraReportGenerators = new ArrayList();
        this.shouldAlwaysGatherSourceMapInfo = false;
        this.sourceMapDetailLevel = DetailLevel.ALL;
        this.sourceMapFormat = Format.DEFAULT;
        this.parseInlineSourceMaps = true;
        this.applyInputSourceMaps = false;
        this.resolveSourceMapAnnotations = true;
        this.sourceMapLocationMappings = ImmutableList.of();
        this.sourceMapIncludeSourcesContent = false;
        this.assumeGettersArePure = true;
        this.assumeStaticInheritanceIsNotUsed = true;
        this.conformanceConfigs = GLOBAL_CONFORMANCE_CONFIGS;
        this.conformanceRemoveRegexFromPath = Optional.of(Pattern.compile("^((.*/)?google3/)?(/?(blaze|bazel)-out/[^/]+/(bin/|(?=genfiles/)))?"));
        this.wrapGoogModulesForWhitespaceOnly = true;
        this.printConfig = false;
        this.isStrictModeInput = Optional.absent();
        this.rewriteModulesBeforeTypechecking = false;
        this.enableModuleRewriting = true;
        this.allowDynamicImport = false;
        this.dynamicImportAlias = null;
        this.es6ModuleTranspilation = CompilerOptions.Es6ModuleTranspilation.COMPILE;
        this.languageIn = CompilerOptions.LanguageMode.STABLE_IN;
        this.environment = CompilerOptions.Environment.BROWSER;
        this.browserResolverPrefixReplacements = ImmutableMap.of();
        this.moduleResolutionMode = ResolutionMode.BROWSER;
        this.packageJsonEntryNames = ImmutableList.of("browser", "module", "main");
        this.pathEscaper = PathEscaper.ESCAPE;
        this.rewriteModulesBeforeTypechecking = false;
        this.enableModuleRewriting = true;
        this.skipNonTranspilationPasses = false;
        this.devMode = CompilerOptions.DevMode.OFF;
        this.checkDeterminism = false;
        this.checkSymbols = false;
        this.checkSuspiciousCode = false;
        this.checkTypes = false;
        this.computeFunctionSideEffects = false;
        this.extraAnnotationNames = null;
        this.foldConstants = false;
        this.coalesceVariableNames = false;
        this.deadAssignmentElimination = false;
        this.inlineConstantVars = false;
        this.inlineFunctionsLevel = CompilerOptions.Reach.NONE;
        this.maxFunctionSizeAfterInlining = -1;
        this.assumeStrictThis = false;
        this.assumeClosuresOnlyCaptureReferences = false;
        this.inlineProperties = false;
        this.crossChunkCodeMotion = false;
        this.parentChunkCanSeeSymbolsDeclaredInChildren = false;
        this.crossChunkMethodMotion = false;
        this.inlineGetters = false;
        this.inlineVariables = false;
        this.inlineLocalVariables = false;
        this.smartNameRemoval = false;
        this.removeUnreachableCode = false;
        this.extractPrototypeMemberDeclarations = CompilerOptions.ExtractPrototypeMemberDeclarationsMode.OFF;
        this.removeUnusedPrototypeProperties = false;
        this.removeUnusedClassProperties = false;
        this.removeUnusedVars = false;
        this.removeUnusedLocalVars = false;
        this.collapseVariableDeclarations = false;
        this.collapseAnonymousFunctions = false;
        this.aliasStringsMode = CompilerOptions.AliasStringsMode.NONE;
        this.outputJsStringUsage = false;
        this.convertToDottedProperties = false;
        this.rewriteFunctionExpressions = false;
        this.variableRenaming = VariableRenamingPolicy.OFF;
        this.propertyRenaming = PropertyRenamingPolicy.OFF;
        this.propertyRenamingOnlyCompilationMode = false;
        this.labelRenaming = false;
        this.generatePseudoNames = false;
        this.preferStableNames = false;
        this.renamePrefix = null;
        this.collapsePropertiesLevel = CompilerOptions.PropertyCollapseLevel.NONE;
        this.collapseObjectLiterals = false;
        this.devirtualizeMethods = false;
        this.disambiguateProperties = false;
        this.ambiguateProperties = false;
        this.exportTestFunctions = false;
        this.nameGenerator = new DefaultNameGenerator();
        this.syntheticBlockStartMarker = null;
        this.syntheticBlockEndMarker = null;
        this.locale = null;
        this.doLateLocalization = false;
        this.markAsCompiled = false;
        this.closurePass = false;
        this.preserveClosurePrimitives = false;
        this.angularPass = false;
        this.polymerVersion = null;
        this.polymerExportPolicy = PolymerExportPolicy.LEGACY;
        this.j2clPassMode = CompilerOptions.J2clPassMode.AUTO;
        this.j2clMinifierEnabled = true;
        this.removeAbstractMethods = false;
        this.removeClosureAsserts = false;
        this.stripTypes = ImmutableSet.of();
        this.stripNameSuffixes = ImmutableSet.of();
        this.stripNamePrefixes = ImmutableSet.of();
        this.customPasses = null;
        this.defineReplacements = new LinkedHashMap();
        this.tweakProcessing = CompilerOptions.TweakProcessing.OFF;
        this.rewriteGlobalDeclarationsForTryCatchWrapping = false;
        this.checksOnly = false;
        this.outputJs = CompilerOptions.OutputJs.NORMAL;
        this.generateExports = true;
        this.exportLocalPropertyDefinitions = true;
        this.cssRenamingMap = null;
        this.cssRenamingSkiplist = null;
        this.idGenerators = ImmutableMap.of();
        this.replaceStringsFunctionDescriptions = ImmutableList.of();
        this.replaceStringsPlaceholderToken = "";
        this.propertiesThatMustDisambiguate = ImmutableSet.of();
        this.inputSourceMaps = ImmutableMap.of();
        this.instrumentForCoverageOption = CompilerOptions.InstrumentOption.NONE;
        this.productionInstrumentationArrayName = "";
        this.preserveTypeAnnotations = false;
        this.gentsMode = false;
        this.printInputDelimiter = false;
        this.prettyPrint = false;
        this.lineBreak = false;
        this.tracer = CompilerOptions.TracerMode.OFF;
        this.colorizeErrorOutput = false;
        this.errorFormat = ErrorFormat.FULL;
        this.chunkOutputType = CompilerOptions.ChunkOutputType.GLOBAL_NAMESPACE;
        this.aliasHandler = NULL_ALIAS_TRANSFORMATION_HANDLER;
        this.errorHandler = null;
        this.printSourceAfterEachPass = false;
        this.strictMessageReplacement = false;
    }

    public boolean isRemoveUnusedClassProperties() {
        return this.removeUnusedClassProperties;
    }

    public void setRemoveUnusedClassProperties(boolean removeUnusedClassProperties) {
        this.removeUnusedClassProperties = removeUnusedClassProperties;
    }

    public ImmutableMap<String, Node> getDefineReplacements() {
        ImmutableMap.Builder<String, Node> map = ImmutableMap.builder();

        for(Map.Entry<String, Object> entry : this.defineReplacements.entrySet()) {
            String name = (String)entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Boolean) {
                map.put(name, NodeUtil.booleanNode((Boolean)value));
            } else if (value instanceof Number) {
                map.put(name, NodeUtil.numberNode(((Number)value).doubleValue(), (Node)null));
            } else {
                if (!(value instanceof String)) {
                    throw new IllegalStateException(String.valueOf(value));
                }

                map.put(name, IR.string((String)value));
            }
        }

        return map.buildOrThrow();
    }

    public void setDefineToBooleanLiteral(String defineName, boolean value) {
        this.defineReplacements.put(defineName, value);
    }

    public void setDefineToStringLiteral(String defineName, String value) {
        this.defineReplacements.put(defineName, value);
    }

    public void setDefineToNumberLiteral(String defineName, int value) {
        this.defineReplacements.put(defineName, value);
    }

    public void setDefineToDoubleLiteral(String defineName, double value) {
        this.defineReplacements.put(defineName, value);
    }

    public void skipAllCompilerPasses() {
        this.skipNonTranspilationPasses = true;
    }

    boolean enables(DiagnosticGroup group) {
        return this.warningsGuard.mustRunChecks(group) == Tri.TRUE;
    }

    boolean disables(DiagnosticGroup group) {
        return this.warningsGuard.mustRunChecks(group) == Tri.FALSE;
    }

    public void setWarningLevel(DiagnosticGroup type, CheckLevel level) {
        this.addWarningsGuard(new DiagnosticGroupWarningsGuard(type, level));
    }

    WarningsGuard getWarningsGuard() {
        return this.warningsGuard;
    }

    public void resetWarningsGuard() {
        this.warningsGuard = new ComposeWarningsGuard(new WarningsGuard[0]);
    }

    public void addWarningsGuard(WarningsGuard guard) {
        this.warningsGuard.addGuard(guard);
    }

    public void setRenamingPolicy(VariableRenamingPolicy newVariablePolicy, PropertyRenamingPolicy newPropertyPolicy) {
        this.variableRenaming = newVariablePolicy;
        this.propertyRenaming = newPropertyPolicy;
    }

    public void setReplaceIdGenerators(boolean replaceIdGenerators) {
        this.replaceIdGenerators = replaceIdGenerators;
    }

    public void setIdGenerators(Set<String> idGenerators) {
        RenamingMap gen = new UniqueRenamingToken();
        ImmutableMap.Builder<String, RenamingMap> builder = ImmutableMap.builder();

        for(String name : idGenerators) {
            builder.put(name, gen);
        }

        this.idGenerators = builder.buildOrThrow();
    }

    public void setIdGenerators(Map<String, RenamingMap> idGenerators) {
        this.idGenerators = ImmutableMap.copyOf(idGenerators);
    }

    public void setIdGeneratorsMap(String previousMappings) {
        this.idGeneratorsMapSerialized = previousMappings;
    }

    public void setXidHashFunction(Xid.HashFunction xidHashFunction) {
        this.xidHashFunction = xidHashFunction;
    }

    public void setChunkIdHashFunction(Xid.HashFunction chunkIdHashFunction) {
        this.chunkIdHashFunction = chunkIdHashFunction;
    }

    public void setInlineFunctions(Reach reach) {
        this.inlineFunctionsLevel = reach;
    }

    public Reach getInlineFunctionsLevel() {
        return this.inlineFunctionsLevel;
    }

    public void setMaxFunctionSizeAfterInlining(int funAstSize) {
        Preconditions.checkArgument(funAstSize > 0);
        this.maxFunctionSizeAfterInlining = funAstSize;
    }

    public void setInlineVariables(boolean inlineVariables) {
        this.inlineVariables = inlineVariables;
    }

    public void setInlineVariables(Reach reach) {
        switch (reach) {
            case ALL:
                this.inlineVariables = true;
                this.inlineLocalVariables = true;
                break;
            case LOCAL_ONLY:
                this.inlineVariables = false;
                this.inlineLocalVariables = true;
                break;
            case NONE:
                this.inlineVariables = false;
                this.inlineLocalVariables = false;
        }

    }

    public void setInlineProperties(boolean enable) {
        this.inlineProperties = enable;
    }

    public boolean shouldInlineProperties() {
        return this.inlineProperties;
    }

    public void setRemoveUnusedVariables(Reach reach) {
        switch (reach) {
            case ALL:
                this.removeUnusedVars = true;
                this.removeUnusedLocalVars = true;
                break;
            case LOCAL_ONLY:
                this.removeUnusedVars = false;
                this.removeUnusedLocalVars = true;
                break;
            case NONE:
                this.removeUnusedVars = false;
                this.removeUnusedLocalVars = false;
        }

    }

    public void setReplaceStringsConfiguration(String placeholderToken, List<String> functionDescriptors) {
        this.replaceStringsPlaceholderToken = placeholderToken;
        this.replaceStringsFunctionDescriptions = new ArrayList(functionDescriptors);
    }

    public void setRemoveAbstractMethods(boolean remove) {
        this.removeAbstractMethods = remove;
    }

    public void setRemoveClosureAsserts(boolean remove) {
        this.removeClosureAsserts = remove;
    }

    public void setRemoveJ2clAsserts(boolean remove) {
        this.removeJ2clAsserts = remove;
    }

    public void setColorizeErrorOutput(boolean colorizeErrorOutput) {
        this.colorizeErrorOutput = colorizeErrorOutput;
    }

    public boolean shouldColorizeErrorOutput() {
        return this.colorizeErrorOutput;
    }

    public void setChecksOnly(boolean checksOnly) {
        this.checksOnly = checksOnly;
    }

    public void setOutputJs(OutputJs outputJs) {
        this.outputJs = outputJs;
    }

    public void setGenerateExports(boolean generateExports) {
        this.generateExports = generateExports;
    }

    public void setExportLocalPropertyDefinitions(boolean export) {
        this.exportLocalPropertyDefinitions = export;
    }

    public boolean shouldExportLocalPropertyDefinitions() {
        return this.exportLocalPropertyDefinitions;
    }

    public void setAngularPass(boolean angularPass) {
        this.angularPass = angularPass;
    }

    public void setPolymerVersion(Integer polymerVersion) {
        Preconditions.checkArgument(polymerVersion == null || polymerVersion == 1 || polymerVersion == 2, "Invalid Polymer version: (%s)", polymerVersion);
        this.polymerVersion = polymerVersion;
    }

    public void setPolymerExportPolicy(PolymerExportPolicy polymerExportPolicy) {
        this.polymerExportPolicy = polymerExportPolicy;
    }

    public void setChromePass(boolean chromePass) {
        this.chromePass = chromePass;
    }

    public boolean isChromePassEnabled() {
        return this.chromePass;
    }

    public void setJ2clPass(J2clPassMode j2clPassMode) {
        this.j2clPassMode = j2clPassMode;
    }

    public void setJ2clMinifierEnabled(boolean enabled) {
        this.j2clMinifierEnabled = enabled;
    }

    public void setJ2clMinifierPruningManifest(String j2clMinifierPruningManifest) {
        this.j2clMinifierPruningManifest = j2clMinifierPruningManifest;
    }

    public void setCodingConvention(CodingConvention codingConvention) {
        this.codingConvention = codingConvention;
    }

    public CodingConvention getCodingConvention() {
        return this.codingConvention;
    }

    public void setDependencyOptions(DependencyOptions dependencyOptions) {
        this.dependencyOptions = dependencyOptions;
    }

    public DependencyOptions getDependencyOptions() {
        return this.dependencyOptions;
    }

    public void setSummaryDetailLevel(int summaryDetailLevel) {
        this.summaryDetailLevel = summaryDetailLevel;
    }

    public void setExtraAnnotationNames(Iterable<String> extraAnnotationNames) {
        this.extraAnnotationNames = ImmutableSet.copyOf(extraAnnotationNames);
    }

    public void setOutputCharset(Charset charset) {
        this.outputCharset = charset;
    }

    Charset getOutputCharset() {
        return this.outputCharset;
    }

    public void setTweakProcessing(TweakProcessing tweakProcessing) {
        this.tweakProcessing = tweakProcessing;
    }

    public TweakProcessing getTweakProcessing() {
        return this.tweakProcessing;
    }

    public void setLanguage(LanguageMode language) {
        Preconditions.checkState(language != CompilerOptions.LanguageMode.NO_TRANSPILE);
        this.setLanguageIn(language);
        this.setLanguageOut(language);
    }

    public void setLanguageIn(LanguageMode languageIn) {
        Preconditions.checkState(languageIn != CompilerOptions.LanguageMode.NO_TRANSPILE);
        this.languageIn = languageIn == CompilerOptions.LanguageMode.STABLE ? CompilerOptions.LanguageMode.STABLE_IN : languageIn;
    }

    public LanguageMode getLanguageIn() {
        return this.languageIn;
    }

    public void setLanguageOut(LanguageMode languageOut) {
        if (languageOut == CompilerOptions.LanguageMode.NO_TRANSPILE) {
            this.languageOutIsDefaultStrict = Optional.absent();
            this.outputFeatureSet = Optional.absent();
        } else {
            languageOut = languageOut == CompilerOptions.LanguageMode.STABLE ? CompilerOptions.LanguageMode.STABLE_OUT : languageOut;
            this.languageOutIsDefaultStrict = Optional.of(languageOut.isDefaultStrict());
            this.setOutputFeatureSet(languageOut.toFeatureSet());
        }

    }

    void setOutputFeatureSet(FeatureSet featureSet) {
        this.outputFeatureSet = Optional.of(featureSet);
    }

    @RestrictedApi(
        explanation = "This API can request unsupported transpilation modes that crash the compiler",
        link = "go/setOutputFeatureSetDeprecation",
        allowlistAnnotations = {LegacySetFeatureSetCaller.class}
    )
    public void legacySetOutputFeatureSet(FeatureSet featureSet) {
        this.setOutputFeatureSet(featureSet);
    }

    public FeatureSet getOutputFeatureSet() {
        return this.outputFeatureSet.isPresent() ? (FeatureSet)this.outputFeatureSet.get() : this.languageIn.toFeatureSet();
    }

    public void setExperimentalForceTranspiles(ExperimentalForceTranspile... experimentalForceTranspile) {
        if (experimentalForceTranspile.length > 1) {
            Preconditions.checkState(!this.experimentalForceTranspiles.contains(CompilerOptions.ExperimentalForceTranspile.ALL_EXCEPT_ASYNC_AWAIT));
        }

        this.experimentalForceTranspiles = ImmutableList.copyOf(experimentalForceTranspile);
    }

    public ImmutableList<ExperimentalForceTranspile> getExperimentalForceTranspiles() {
        return this.experimentalForceTranspiles;
    }

    public boolean needsTranspilationFrom(FeatureSet languageLevel) {
        return this.getLanguageIn().toFeatureSet().contains(languageLevel) && !this.getOutputFeatureSet().contains(languageLevel);
    }

    public boolean needsTranspilationOf(FeatureSet.Feature feature) {
        return this.getLanguageIn().toFeatureSet().has(feature) && !this.getOutputFeatureSet().has(feature);
    }

    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    public Environment getEnvironment() {
        return this.environment;
    }

    public void setAliasTransformationHandler(AliasTransformationHandler changes) {
        this.aliasHandler = changes;
    }

    public AliasTransformationHandler getAliasTransformationHandler() {
        return this.aliasHandler;
    }

    public void setErrorHandler(ErrorHandler handler) {
        this.errorHandler = handler;
    }

    public void setInferTypes(boolean enable) {
        this.inferTypes = enable;
    }

    public boolean getInferTypes() {
        return this.inferTypes;
    }

    /** @deprecated */
    @Deprecated
    public void setNewTypeInference(boolean enable) {
    }

    public void setAllowZoneJsWithAsyncFunctionsInOutput(boolean enable) {
        this.allowZoneJsWithAsyncFunctionsInOutput = enable;
    }

    boolean allowsZoneJsWithAsyncFunctionsInOutput() {
        return this.checksOnly || this.allowZoneJsWithAsyncFunctionsInOutput;
    }

    public boolean isTypecheckingEnabled() {
        return this.checkTypes;
    }

    public boolean assumeStrictThis() {
        return this.assumeStrictThis;
    }

    public void setAssumeStrictThis(boolean enable) {
        this.assumeStrictThis = enable;
    }

    public boolean assumeClosuresOnlyCaptureReferences() {
        return this.assumeClosuresOnlyCaptureReferences;
    }

    public void setAssumeClosuresOnlyCaptureReferences(boolean enable) {
        this.assumeClosuresOnlyCaptureReferences = enable;
    }

    public void setPropertiesThatMustDisambiguate(Set<String> names) {
        this.propertiesThatMustDisambiguate = ImmutableSet.copyOf(names);
    }

    public ImmutableSet<String> getPropertiesThatMustDisambiguate() {
        return this.propertiesThatMustDisambiguate;
    }

    public void setPreserveDetailedSourceInfo(boolean preserveDetailedSourceInfo) {
        this.preserveDetailedSourceInfo = preserveDetailedSourceInfo;
    }

    boolean preservesDetailedSourceInfo() {
        return this.preserveDetailedSourceInfo;
    }

    public void setPreserveNonJSDocComments(boolean preserveNonJSDocComments) {
        this.preserveNonJSDocComments = preserveNonJSDocComments;
    }

    boolean getPreserveNonJSDocComments() {
        return this.preserveNonJSDocComments;
    }

    public void setContinueAfterErrors(boolean continueAfterErrors) {
        this.continueAfterErrors = continueAfterErrors;
    }

    boolean canContinueAfterErrors() {
        return this.continueAfterErrors;
    }

    public void setParseJsDocDocumentation(Config.JsDocParsing parseJsDocDocumentation) {
        this.parseJsDocDocumentation = parseJsDocDocumentation;
    }

    public Config.JsDocParsing isParseJsDocDocumentation() {
        return this.parseJsDocDocumentation;
    }

    public void setSkipNonTranspilationPasses(boolean skipNonTranspilationPasses) {
        this.skipNonTranspilationPasses = skipNonTranspilationPasses;
    }

    public void setDevMode(DevMode devMode) {
        this.devMode = devMode;
    }

    public void setCheckDeterminism(boolean checkDeterminism) {
        this.checkDeterminism = checkDeterminism;
    }

    public boolean getCheckDeterminism() {
        return this.checkDeterminism;
    }

    public void setMessageBundle(MessageBundle messageBundle) {
        this.messageBundle = messageBundle;
    }

    public void setCheckSymbols(boolean checkSymbols) {
        this.checkSymbols = checkSymbols;
    }

    public void setCheckSuspiciousCode(boolean checkSuspiciousCode) {
        this.checkSuspiciousCode = checkSuspiciousCode;
    }

    public void setCheckTypes(boolean checkTypes) {
        this.checkTypes = checkTypes;
    }

    public void setFoldConstants(boolean foldConstants) {
        this.foldConstants = foldConstants;
    }

    public void setDeadAssignmentElimination(boolean deadAssignmentElimination) {
        this.deadAssignmentElimination = deadAssignmentElimination;
    }

    public void setInlineConstantVars(boolean inlineConstantVars) {
        this.inlineConstantVars = inlineConstantVars;
    }

    public void setCrossChunkCodeMotion(boolean crossChunkCodeMotion) {
        this.crossChunkCodeMotion = crossChunkCodeMotion;
    }

    public void setCrossChunkCodeMotionNoStubMethods(boolean crossChunkCodeMotionNoStubMethods) {
        this.crossChunkCodeMotionNoStubMethods = crossChunkCodeMotionNoStubMethods;
    }

    public void setParentChunkCanSeeSymbolsDeclaredInChildren(boolean parentChunkCanSeeSymbolsDeclaredInChildren) {
        this.parentChunkCanSeeSymbolsDeclaredInChildren = parentChunkCanSeeSymbolsDeclaredInChildren;
    }

    public void setCrossChunkMethodMotion(boolean crossChunkMethodMotion) {
        this.crossChunkMethodMotion = crossChunkMethodMotion;
    }

    public void setCoalesceVariableNames(boolean coalesceVariableNames) {
        this.coalesceVariableNames = coalesceVariableNames;
    }

    public void setInlineLocalVariables(boolean inlineLocalVariables) {
        this.inlineLocalVariables = inlineLocalVariables;
    }

    public void setFlowSensitiveInlineVariables(boolean enabled) {
        this.flowSensitiveInlineVariables = enabled;
    }

    public void setSmartNameRemoval(boolean smartNameRemoval) {
        this.smartNameRemoval = smartNameRemoval;
        if (smartNameRemoval) {
            this.removeUnusedVars = true;
            this.removeUnusedPrototypeProperties = true;
        }

    }

    /** @deprecated */
    @Deprecated
    @InlineMe(
        replacement = "this.setRemoveUnreachableCode(removeUnreachableCode)"
    )
    public final void setRemoveDeadCode(boolean removeUnreachableCode) {
        this.setRemoveUnreachableCode(removeUnreachableCode);
    }

    public void setRemoveUnreachableCode(boolean removeUnreachableCode) {
        this.removeUnreachableCode = removeUnreachableCode;
    }

    public void setExtractPrototypeMemberDeclarations(boolean enabled) {
        this.extractPrototypeMemberDeclarations = enabled ? CompilerOptions.ExtractPrototypeMemberDeclarationsMode.USE_GLOBAL_TEMP : CompilerOptions.ExtractPrototypeMemberDeclarationsMode.OFF;
    }

    public void setExtractPrototypeMemberDeclarations(ExtractPrototypeMemberDeclarationsMode mode) {
        this.extractPrototypeMemberDeclarations = mode;
    }

    public void setRemoveUnusedPrototypeProperties(boolean enabled) {
        this.removeUnusedPrototypeProperties = enabled;
        this.inlineGetters = enabled;
    }

    public void setCollapseVariableDeclarations(boolean enabled) {
        this.collapseVariableDeclarations = enabled;
    }

    public void setCollapseAnonymousFunctions(boolean enabled) {
        this.collapseAnonymousFunctions = enabled;
    }

    public void setAliasStringsMode(AliasStringsMode aliasStringsMode) {
        this.aliasStringsMode = aliasStringsMode;
    }

    public AliasStringsMode getAliasStringsMode() {
        return this.aliasStringsMode;
    }

    public void setOutputJsStringUsage(boolean outputJsStringUsage) {
        this.outputJsStringUsage = outputJsStringUsage;
    }

    public void setConvertToDottedProperties(boolean convertToDottedProperties) {
        this.convertToDottedProperties = convertToDottedProperties;
    }

    public void setUseTypesForLocalOptimization(boolean useTypesForLocalOptimization) {
        this.useTypesForLocalOptimization = useTypesForLocalOptimization;
    }

    public boolean shouldUseTypesForLocalOptimization() {
        return this.useTypesForLocalOptimization;
    }

    /** @deprecated */
    @Deprecated
    public void setUseTypesForOptimization(boolean useTypesForOptimization) {
        if (useTypesForOptimization) {
            this.disambiguateProperties = true;
            this.ambiguateProperties = true;
            this.inlineProperties = true;
            this.useTypesForLocalOptimization = true;
        }

    }

    boolean requiresTypesForOptimization() {
        return this.disambiguateProperties || this.ambiguateProperties || this.inlineProperties || this.useTypesForLocalOptimization;
    }

    public void setRewriteFunctionExpressions(boolean rewriteFunctionExpressions) {
        this.rewriteFunctionExpressions = rewriteFunctionExpressions;
    }

    public void setOptimizeCalls(boolean optimizeCalls) {
        this.optimizeCalls = optimizeCalls;
    }

    public boolean getOptimizeESClassConstructors() {
        return this.optimizeESClassConstructors;
    }

    public void setOptimizeESClassConstructors(boolean optimizeESClassConstructors) {
        this.optimizeESClassConstructors = optimizeESClassConstructors;
    }

    public void setOptimizeArgumentsArray(boolean optimizeArgumentsArray) {
        this.optimizeArgumentsArray = optimizeArgumentsArray;
    }

    public void setVariableRenaming(VariableRenamingPolicy variableRenaming) {
        this.variableRenaming = variableRenaming;
    }

    public void setPropertyRenaming(PropertyRenamingPolicy propertyRenaming) {
        this.propertyRenaming = propertyRenaming;
    }

    public PropertyRenamingPolicy getPropertyRenaming() {
        return this.propertyRenaming;
    }

    public void setLabelRenaming(boolean labelRenaming) {
        this.labelRenaming = labelRenaming;
    }

    public void setReserveRawExports(boolean reserveRawExports) {
        this.reserveRawExports = reserveRawExports;
    }

    public void setPreferStableNames(boolean preferStableNames) {
        this.preferStableNames = preferStableNames;
    }

    public void setGeneratePseudoNames(boolean generatePseudoNames) {
        this.generatePseudoNames = generatePseudoNames;
    }

    public void setPropertyRenamingOnlyCompilationMode(boolean propertyRenamingOnlyCompilationMode) {
        this.propertyRenamingOnlyCompilationMode = propertyRenamingOnlyCompilationMode;
    }

    public boolean isPropertyRenamingOnlyCompilationMode() {
        return this.propertyRenamingOnlyCompilationMode;
    }

    public void setRenamePrefix(String renamePrefix) {
        this.renamePrefix = renamePrefix;
    }

    public String getRenamePrefixNamespace() {
        return this.renamePrefixNamespace;
    }

    public void setRenamePrefixNamespace(String renamePrefixNamespace) {
        this.renamePrefixNamespace = renamePrefixNamespace;
    }

    public void setCollapsePropertiesLevel(PropertyCollapseLevel level) {
        this.collapsePropertiesLevel = level;
    }

    /** @deprecated */
    @Deprecated
    public void setCollapseProperties(boolean fullyCollapse) {
        this.collapsePropertiesLevel = fullyCollapse ? CompilerOptions.PropertyCollapseLevel.ALL : CompilerOptions.PropertyCollapseLevel.NONE;
    }

    public void setDevirtualizeMethods(boolean devirtualizeMethods) {
        this.devirtualizeMethods = devirtualizeMethods;
    }

    public void setComputeFunctionSideEffects(boolean computeFunctionSideEffects) {
        this.computeFunctionSideEffects = computeFunctionSideEffects;
    }

    public void setDisambiguateProperties(boolean disambiguateProperties) {
        this.disambiguateProperties = disambiguateProperties;
    }

    public boolean shouldDisambiguateProperties() {
        return this.disambiguateProperties;
    }

    public void setAmbiguateProperties(boolean ambiguateProperties) {
        this.ambiguateProperties = ambiguateProperties;
    }

    public boolean shouldAmbiguateProperties() {
        return this.ambiguateProperties;
    }

    public void setInputVariableMap(VariableMap inputVariableMap) {
        this.inputVariableMap = inputVariableMap;
    }

    public void setInputPropertyMap(VariableMap inputPropertyMap) {
        this.inputPropertyMap = inputPropertyMap;
    }

    public void setExportTestFunctions(boolean exportTestFunctions) {
        this.exportTestFunctions = exportTestFunctions;
    }

    public void setSyntheticBlockStartMarker(String syntheticBlockStartMarker) {
        this.syntheticBlockStartMarker = syntheticBlockStartMarker;
    }

    public void setSyntheticBlockEndMarker(String syntheticBlockEndMarker) {
        this.syntheticBlockEndMarker = syntheticBlockEndMarker;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public void setDoLateLocalization(boolean doLateLocalization) {
        this.doLateLocalization = doLateLocalization;
    }

    public boolean doLateLocalization() {
        return this.doLateLocalization;
    }

    public boolean shouldRunReplaceMessagesPass() {
        return !this.shouldRunReplaceMessagesForChrome() && this.messageBundle != null;
    }

    public void setMarkAsCompiled(boolean markAsCompiled) {
        this.markAsCompiled = markAsCompiled;
    }

    public void setClosurePass(boolean closurePass) {
        this.closurePass = closurePass;
    }

    public void setPreserveClosurePrimitives(boolean preserveClosurePrimitives) {
        this.preserveClosurePrimitives = preserveClosurePrimitives;
    }

    public boolean shouldPreservesGoogProvidesAndRequires() {
        return this.preserveClosurePrimitives;
    }

    public boolean shouldPreserveGoogModule() {
        return this.preserveClosurePrimitives;
    }

    public boolean shouldPreserveGoogLibraryPrimitives() {
        return this.preserveClosurePrimitives;
    }

    public void setPreserveTypeAnnotations(boolean preserveTypeAnnotations) {
        this.preserveTypeAnnotations = preserveTypeAnnotations;
    }

    public void setGentsMode(boolean gentsMode) {
        this.gentsMode = gentsMode;
    }

    public void setGatherCssNames(boolean gatherCssNames) {
        this.gatherCssNames = gatherCssNames;
    }

    /** @deprecated */
    @Deprecated
    public void setStripTypes(Set<String> stripTypes) {
        this.stripTypes = ImmutableSet.copyOf(stripTypes);
    }

    /** @deprecated */
    @Deprecated
    public ImmutableSet<String> getStripTypes() {
        return this.stripTypes;
    }

    /** @deprecated */
    @Deprecated
    public void setStripNameSuffixes(Set<String> stripNameSuffixes) {
        this.stripNameSuffixes = ImmutableSet.copyOf(stripNameSuffixes);
    }

    /** @deprecated */
    @Deprecated
    public void setStripNamePrefixes(Set<String> stripNamePrefixes) {
        this.stripNamePrefixes = ImmutableSet.copyOf(stripNamePrefixes);
    }

    public void addCustomPass(CustomPassExecutionTime time, CompilerPass customPass) {
        if (this.customPasses == null) {
            this.customPasses = LinkedHashMultimap.create();
        }

        this.customPasses.put(time, customPass);
    }

    public void setDefineReplacements(Map<String, Object> defineReplacements) {
        this.defineReplacements.clear();
        this.defineReplacements.putAll(defineReplacements);
    }

    public void setRewriteGlobalDeclarationsForTryCatchWrapping(boolean rewrite) {
        this.rewriteGlobalDeclarationsForTryCatchWrapping = rewrite;
    }

    public void setCssRenamingMap(CssRenamingMap cssRenamingMap) {
        this.cssRenamingMap = cssRenamingMap;
    }

    public void setCssRenamingSkiplist(Set<String> skiplist) {
        this.cssRenamingSkiplist = skiplist;
    }

    public void setReplaceStringsFunctionDescriptions(List<String> replaceStringsFunctionDescriptions) {
        this.replaceStringsFunctionDescriptions = replaceStringsFunctionDescriptions;
    }

    public void setReplaceStringsPlaceholderToken(String replaceStringsPlaceholderToken) {
        this.replaceStringsPlaceholderToken = replaceStringsPlaceholderToken;
    }

    public void setPrettyPrint(boolean prettyPrint) {
        this.prettyPrint = prettyPrint;
    }

    public boolean isPrettyPrint() {
        return this.prettyPrint;
    }

    public void setLineBreak(boolean lineBreak) {
        this.lineBreak = lineBreak;
    }

    public void setPrintInputDelimiter(boolean printInputDelimiter) {
        this.printInputDelimiter = printInputDelimiter;
    }

    public void setInputDelimiter(String inputDelimiter) {
        this.inputDelimiter = inputDelimiter;
    }

    public void setDebugLogDirectory(@Nullable Path dir) {
        this.debugLogDirectory = dir;
    }

    public @Nullable Path getDebugLogDirectory() {
        return this.debugLogDirectory;
    }

    public void setDebugLogFilter(String filter) {
        this.debugLogFilter = filter;
    }

    public String getDebugLogFilter() {
        return this.debugLogFilter;
    }

    boolean shouldSerializeExtraDebugInfo() {
        return this.serializeExtraDebugInfo || this.getDebugLogDirectory() != null;
    }

    void setSerializeExtraDebugInfo(boolean serializeExtraDebugInfo) {
        this.serializeExtraDebugInfo = serializeExtraDebugInfo;
    }

    public void setQuoteKeywordProperties(boolean quoteKeywordProperties) {
        this.quoteKeywordProperties = quoteKeywordProperties;
    }

    public boolean shouldQuoteKeywordProperties() {
        if (this.incrementalCheckMode == CompilerOptions.IncrementalCheckMode.GENERATE_IJS) {
            return false;
        } else {
            return this.quoteKeywordProperties || FeatureSet.ES3.contains(this.getOutputFeatureSet());
        }
    }

    public void setErrorFormat(ErrorFormat errorFormat) {
        this.errorFormat = errorFormat;
    }

    public ErrorFormat getErrorFormat() {
        return this.errorFormat;
    }

    public void setWarningsGuard(ComposeWarningsGuard warningsGuard) {
        this.warningsGuard = warningsGuard;
    }

    public void setLineLengthThreshold(int lineLengthThreshold) {
        this.lineLengthThreshold = lineLengthThreshold;
    }

    public int getLineLengthThreshold() {
        return this.lineLengthThreshold;
    }

    public void setUseOriginalNamesInOutput(boolean useOriginalNamesInOutput) {
        this.useOriginalNamesInOutput = useOriginalNamesInOutput;
    }

    public boolean getUseOriginalNamesInOutput() {
        return this.useOriginalNamesInOutput;
    }

    public void setExternExportsPath(@Nullable String externExportsPath) {
        this.externExportsPath = externExportsPath;
    }

    public @Nullable String getExternExportsPath() {
        return this.externExportsPath;
    }

    public void setSourceMapOutputPath(String sourceMapOutputPath) {
        this.sourceMapOutputPath = sourceMapOutputPath;
    }

    public void setApplyInputSourceMaps(boolean applyInputSourceMaps) {
        this.applyInputSourceMaps = applyInputSourceMaps;
    }

    public void setResolveSourceMapAnnotations(boolean resolveSourceMapAnnotations) {
        this.resolveSourceMapAnnotations = resolveSourceMapAnnotations;
    }

    public void setSourceMapIncludeSourcesContent(boolean sourceMapIncludeSourcesContent) {
        this.sourceMapIncludeSourcesContent = sourceMapIncludeSourcesContent;
    }

    public void setParseInlineSourceMaps(boolean parseInlineSourceMaps) {
        this.parseInlineSourceMaps = parseInlineSourceMaps;
    }

    public void setSourceMapDetailLevel(SourceMap.DetailLevel sourceMapDetailLevel) {
        this.sourceMapDetailLevel = sourceMapDetailLevel;
    }

    public void setSourceMapFormat(SourceMap.Format sourceMapFormat) {
        this.sourceMapFormat = sourceMapFormat;
    }

    public void setSourceMapLocationMappings(List<? extends SourceMap.LocationMapping> sourceMapLocationMappings) {
        this.sourceMapLocationMappings = sourceMapLocationMappings;
    }

    public void setProcessCommonJSModules(boolean processCommonJSModules) {
        this.processCommonJSModules = processCommonJSModules;
    }

    public boolean getProcessCommonJSModules() {
        return this.processCommonJSModules;
    }

    public void setEs6ModuleTranspilation(Es6ModuleTranspilation value) {
        this.es6ModuleTranspilation = value;
    }

    public Es6ModuleTranspilation getEs6ModuleTranspilation() {
        return this.es6ModuleTranspilation;
    }

    public void setCommonJSModulePathPrefix(String commonJSModulePathPrefix) {
        this.setModuleRoots(ImmutableList.of(commonJSModulePathPrefix));
    }

    public void setModuleRoots(List<String> moduleRoots) {
        this.moduleRoots = moduleRoots;
    }

    public void setRewritePolyfills(boolean rewritePolyfills) {
        this.rewritePolyfills = rewritePolyfills;
    }

    public boolean getRewritePolyfills() {
        return this.rewritePolyfills;
    }

    public void setIsolatePolyfills(boolean isolatePolyfills) {
        this.isolatePolyfills = isolatePolyfills;
        if (this.isolatePolyfills) {
            this.setDefineToBooleanLiteral("$jscomp.ISOLATE_POLYFILLS", isolatePolyfills);
        }

    }

    public boolean getIsolatePolyfills() {
        return this.isolatePolyfills;
    }

    public void setForceLibraryInjection(Iterable<String> libraries) {
        this.forceLibraryInjection = ImmutableList.copyOf(libraries);
    }

    public void setPreventLibraryInjection(boolean preventLibraryInjection) {
        this.preventLibraryInjection = preventLibraryInjection;
    }

    public void setUnusedImportsToRemove(@Nullable ImmutableSet<String> unusedImportsToRemove) {
        this.unusedImportsToRemove = unusedImportsToRemove;
    }

    public @Nullable ImmutableSet<String> getUnusedImportsToRemove() {
        return this.unusedImportsToRemove;
    }

    public void setInstrumentForCoverageOption(InstrumentOption instrumentForCoverageOption) {
        this.instrumentForCoverageOption = (InstrumentOption)Preconditions.checkNotNull(instrumentForCoverageOption);
    }

    public InstrumentOption getInstrumentForCoverageOption() {
        return this.instrumentForCoverageOption;
    }

    public void setProductionInstrumentationArrayName(String productionInstrumentationArrayName) {
        this.productionInstrumentationArrayName = (String)Preconditions.checkNotNull(productionInstrumentationArrayName);
    }

    public String getProductionInstrumentationArrayName() {
        return this.productionInstrumentationArrayName;
    }

    public final ImmutableList<ConformanceConfig> getConformanceConfigs() {
        return this.conformanceConfigs;
    }

    @GwtIncompatible("Conformance")
    public void setConformanceConfig(ConformanceConfig conformanceConfig) {
        this.setConformanceConfigs(ImmutableList.of(conformanceConfig));
    }

    @GwtIncompatible("Conformance")
    public void setConformanceConfigs(List<ConformanceConfig> configs) {
        this.conformanceConfigs = ImmutableList.builder().add(ResourceLoader.loadGlobalConformance(CompilerOptions.class)).addAll(configs).build();
    }

    public void clearConformanceConfigs() {
        this.conformanceConfigs = ImmutableList.of();
    }

    public boolean shouldEmitUseStrict() {
        return (Boolean)this.emitUseStrict.or(this.languageOutIsDefaultStrict).or(this.languageIn.isDefaultStrict());
    }

    @CanIgnoreReturnValue
    public CompilerOptions setEmitUseStrict(boolean emitUseStrict) {
        this.emitUseStrict = Optional.of(emitUseStrict);
        return this;
    }

    public ModuleLoader.ResolutionMode getModuleResolutionMode() {
        return this.moduleResolutionMode;
    }

    public void setModuleResolutionMode(ModuleLoader.ResolutionMode moduleResolutionMode) {
        this.moduleResolutionMode = moduleResolutionMode;
    }

    public ImmutableMap<String, String> getBrowserResolverPrefixReplacements() {
        return this.browserResolverPrefixReplacements;
    }

    public void setBrowserResolverPrefixReplacements(ImmutableMap<String, String> browserResolverPrefixReplacements) {
        this.browserResolverPrefixReplacements = browserResolverPrefixReplacements;
    }

    public void setPathEscaper(ModuleLoader.PathEscaper pathEscaper) {
        this.pathEscaper = pathEscaper;
    }

    public ModuleLoader.PathEscaper getPathEscaper() {
        return this.pathEscaper;
    }

    public List<String> getPackageJsonEntryNames() {
        return this.packageJsonEntryNames;
    }

    public void setPackageJsonEntryNames(List<String> names) {
        this.packageJsonEntryNames = names;
    }

    public void setUseSizeHeuristicToStopOptimizationLoop(boolean mayStopEarly) {
        this.useSizeHeuristicToStopOptimizationLoop = mayStopEarly;
    }

    public void setMaxOptimizationLoopIterations(int maxIterations) {
        this.optimizationLoopMaxIterations = maxIterations;
    }

    public ChunkOutputType getChunkOutputType() {
        return this.chunkOutputType;
    }

    public void setChunkOutputType(ChunkOutputType chunkOutputType) {
        this.chunkOutputType = chunkOutputType;
    }

    @GwtIncompatible("ObjectOutputStream")
    public void serialize(OutputStream objectOutputStream) throws IOException {
        (new ObjectOutputStream(objectOutputStream)).writeObject(this);
    }

    @GwtIncompatible("ObjectInputStream")
    public static CompilerOptions deserialize(InputStream objectInputStream) throws IOException, ClassNotFoundException {
        return (CompilerOptions)(new ObjectInputStream(objectInputStream)).readObject();
    }

    public void setStrictMessageReplacement(boolean strictMessageReplacement) {
        this.strictMessageReplacement = strictMessageReplacement;
    }

    public boolean getStrictMessageReplacement() {
        return this.strictMessageReplacement;
    }

    public String toString() {
        return MoreObjects.toStringHelper(this).omitNullValues().add("aliasHandler", this.getAliasTransformationHandler()).add("aliasStringsMode", this.getAliasStringsMode()).add("ambiguateProperties", this.ambiguateProperties).add("angularPass", this.angularPass).add("assumeClosuresOnlyCaptureReferences", this.assumeClosuresOnlyCaptureReferences).add("assumeGettersArePure", this.assumeGettersArePure).add("assumeStrictThis", this.assumeStrictThis()).add("browserResolverPrefixReplacements", this.browserResolverPrefixReplacements).add("checkDeterminism", this.getCheckDeterminism()).add("checkSuspiciousCode", this.checkSuspiciousCode).add("checkSymbols", this.checkSymbols).add("checkTypes", this.checkTypes).add("checksOnly", this.checksOnly).add("chunksToPrintAfterEachPassRegexList", this.chunksToPrintAfterEachPassRegexList).add("closurePass", this.closurePass).add("coalesceVariableNames", this.coalesceVariableNames).add("codingConvention", this.getCodingConvention()).add("collapseAnonymousFunctions", this.collapseAnonymousFunctions).add("collapseObjectLiterals", this.collapseObjectLiterals).add("collapseProperties", this.collapsePropertiesLevel).add("collapseVariableDeclarations", this.collapseVariableDeclarations).add("colorizeErrorOutput", this.shouldColorizeErrorOutput()).add("computeFunctionSideEffects", this.computeFunctionSideEffects).add("conformanceConfigs", this.getConformanceConfigs()).add("conformanceRemoveRegexFromPath", this.conformanceRemoveRegexFromPath).add("continueAfterErrors", this.canContinueAfterErrors()).add("convertToDottedProperties", this.convertToDottedProperties).add("crossChunkCodeMotion", this.crossChunkCodeMotion).add("crossChunkCodeMotionNoStubMethods", this.crossChunkCodeMotionNoStubMethods).add("crossChunkMethodMotion", this.crossChunkMethodMotion).add("cssRenamingMap", this.cssRenamingMap).add("cssRenamingSkiplist", this.cssRenamingSkiplist).add("customPasses", this.customPasses).add("deadAssignmentElimination", this.deadAssignmentElimination).add("debugLogDirectory", this.debugLogDirectory).add("defineReplacements", this.getDefineReplacements()).add("dependencyOptions", this.getDependencyOptions()).add("devMode", this.devMode).add("devirtualizeMethods", this.devirtualizeMethods).add("disambiguateProperties", this.disambiguateProperties).add("emitUseStrict", this.emitUseStrict).add("enableModuleRewriting", this.enableModuleRewriting).add("environment", this.getEnvironment()).add("errorFormat", this.errorFormat).add("errorHandler", this.errorHandler).add("es6ModuleTranspilation", this.es6ModuleTranspilation).add("exportLocalPropertyDefinitions", this.exportLocalPropertyDefinitions).add("exportTestFunctions", this.exportTestFunctions).add("externExportsPath", this.externExportsPath).add("extraAnnotationNames", this.extraAnnotationNames).add("extractPrototypeMemberDeclarations", this.extractPrototypeMemberDeclarations).add("filesToPrintAfterEachPassRegexList", this.filesToPrintAfterEachPassRegexList).add("flowSensitiveInlineVariables", this.flowSensitiveInlineVariables).add("foldConstants", this.foldConstants).add("forceLibraryInjection", this.forceLibraryInjection).add("gatherCssNames", this.gatherCssNames).add("generateExports", this.generateExports).add("generatePseudoNames", this.generatePseudoNames).add("generateTypedExterns", this.shouldGenerateTypedExterns()).add("idGenerators", this.idGenerators).add("idGeneratorsMapSerialized", this.idGeneratorsMapSerialized).add("incrementalCheckMode", this.incrementalCheckMode).add("inferConsts", this.inferConsts).add("inferTypes", this.inferTypes).add("inlineConstantVars", this.inlineConstantVars).add("inlineFunctionsLevel", this.inlineFunctionsLevel).add("inlineGetters", this.inlineGetters).add("inlineLocalVariables", this.inlineLocalVariables).add("inlineProperties", this.inlineProperties).add("inlineVariables", this.inlineVariables).add("inputDelimiter", this.inputDelimiter).add("inputPropertyMap", this.inputPropertyMap).add("inputSourceMaps", this.inputSourceMaps).add("inputVariableMap", this.inputVariableMap).add("instrumentForCoverageOnly", this.instrumentForCoverageOnly).add("instrumentForCoverageOption", this.instrumentForCoverageOption.toString()).add("isolatePolyfills", this.isolatePolyfills).add("j2clMinifierEnabled", this.j2clMinifierEnabled).add("j2clMinifierPruningManifest", this.j2clMinifierPruningManifest).add("j2clPassMode", this.j2clPassMode).add("labelRenaming", this.labelRenaming).add("languageIn", this.getLanguageIn()).add("languageOutIsDefaultStrict", this.languageOutIsDefaultStrict).add("lineBreak", this.lineBreak).add("lineLengthThreshold", this.lineLengthThreshold).add("locale", this.locale).add("markAsCompiled", this.markAsCompiled).add("maxFunctionSizeAfterInlining", this.maxFunctionSizeAfterInlining).add("messageBundle", this.messageBundle).add("moduleRoots", this.moduleRoots).add("nameGenerator", this.nameGenerator).add("numParallelThreads", this.numParallelThreads).add("optimizeArgumentsArray", this.optimizeArgumentsArray).add("optimizeCalls", this.optimizeCalls).add("optimizeESClassConstructors", this.optimizeESClassConstructors).add("outputCharset", this.outputCharset).add("outputFeatureSet", this.outputFeatureSet).add("outputJs", this.outputJs).add("outputJsStringUsage", this.outputJsStringUsage).add("parentChunkCanSeeSymbolsDeclaredInChildren", this.parentChunkCanSeeSymbolsDeclaredInChildren).add("parseJsDocDocumentation", this.isParseJsDocDocumentation()).add("pathEscaper", this.pathEscaper).add("polymerExportPolicy", this.polymerExportPolicy).add("polymerVersion", this.polymerVersion).add("preferSingleQuotes", this.preferSingleQuotes).add("preferStableNames", this.preferStableNames).add("preserveDetailedSourceInfo", this.preservesDetailedSourceInfo()).add("preserveGoogProvidesAndRequires", this.preserveClosurePrimitives).add("preserveNonJSDocComments", this.getPreserveNonJSDocComments()).add("preserveTypeAnnotations", this.preserveTypeAnnotations).add("prettyPrint", this.prettyPrint).add("preventLibraryInjection", this.preventLibraryInjection).add("printConfig", this.printConfig).add("printInputDelimiter", this.printInputDelimiter).add("printSourceAfterEachPass", this.printSourceAfterEachPass).add("processCommonJSModules", this.processCommonJSModules).add("productionInstrumentationArrayName", this.productionInstrumentationArrayName).add("propertiesThatMustDisambiguate", this.propertiesThatMustDisambiguate).add("propertyRenaming", this.propertyRenaming).add("propertyRenamingOnlyCompilationMode", this.propertyRenamingOnlyCompilationMode).add("protectHiddenSideEffects", this.protectHiddenSideEffects).add("qnameUsesToPrintAfterEachPassRegexList", this.qnameUsesToPrintAfterEachPassList).add("quoteKeywordProperties", this.quoteKeywordProperties).add("removeAbstractMethods", this.removeAbstractMethods).add("removeClosureAsserts", this.removeClosureAsserts).add("removeJ2clAsserts", this.removeJ2clAsserts).add("removeUnreachableCode", this.removeUnreachableCode).add("removeUnusedClassProperties", this.removeUnusedClassProperties).add("removeUnusedConstructorProperties", this.removeUnusedConstructorProperties).add("removeUnusedLocalVars", this.removeUnusedLocalVars).add("removeUnusedPrototypeProperties", this.removeUnusedPrototypeProperties).add("removeUnusedVars", this.removeUnusedVars).add("renamePrefix", this.renamePrefix).add("renamePrefixNamespace", this.renamePrefixNamespace).add("renamePrefixNamespaceAssumeCrossChunkNames", this.renamePrefixNamespaceAssumeCrossChunkNames).add("replaceIdGenerators", this.replaceIdGenerators).add("replaceMessagesWithChromeI18n", this.replaceMessagesWithChromeI18n).add("replaceStringsFunctionDescriptions", this.replaceStringsFunctionDescriptions).add("replaceStringsPlaceholderToken", this.replaceStringsPlaceholderToken).add("reserveRawExports", this.reserveRawExports).add("rewriteFunctionExpressions", this.rewriteFunctionExpressions).add("rewriteGlobalDeclarationsForTryCatchWrapping", this.rewriteGlobalDeclarationsForTryCatchWrapping).add("rewriteModulesBeforeTypechecking", this.rewriteModulesBeforeTypechecking).add("rewritePolyfills", this.rewritePolyfills).add("skipNonTranspilationPasses", this.skipNonTranspilationPasses).add("smartNameRemoval", this.smartNameRemoval).add("sourceMapDetailLevel", this.sourceMapDetailLevel).add("sourceMapFormat", this.sourceMapFormat).add("sourceMapLocationMappings", this.sourceMapLocationMappings).add("sourceMapOutputPath", this.sourceMapOutputPath).add("strictMessageReplacement", this.strictMessageReplacement).add("stripNamePrefixes", this.stripNamePrefixes).add("stripNameSuffixes", this.stripNameSuffixes).add("stripTypes", this.stripTypes).add("summaryDetailLevel", this.summaryDetailLevel).add("syntheticBlockEndMarker", this.syntheticBlockEndMarker).add("syntheticBlockStartMarker", this.syntheticBlockStartMarker).add("tcProjectId", this.tcProjectId).add("tracer", this.tracer).add("trustedStrings", this.trustedStrings).add("tweakProcessing", this.getTweakProcessing()).add("unusedImportsToRemove", this.unusedImportsToRemove).add("useTypesForLocalOptimization", this.useTypesForLocalOptimization).add("variableRenaming", this.variableRenaming).add("warningsGuard", this.getWarningsGuard()).add("wrapGoogModulesForWhitespaceOnly", this.wrapGoogModulesForWhitespaceOnly).toString();
    }

    public boolean expectStrictModeInput() {
        return (Boolean)this.isStrictModeInput.or(this.getLanguageIn().isDefaultStrict());
    }

    @CanIgnoreReturnValue
    public CompilerOptions setStrictModeInput(boolean isStrictModeInput) {
        this.isStrictModeInput = Optional.of(isStrictModeInput);
        return this;
    }

    public char[] getPropertyReservedNamingFirstChars() {
        char[] reservedChars = null;
        if (this.polymerVersion != null && this.polymerVersion > 1) {
            if (reservedChars == null) {
                reservedChars = POLYMER_PROPERTY_RESERVED_FIRST_CHARS;
            } else {
                reservedChars = Chars.concat(new char[][]{reservedChars, POLYMER_PROPERTY_RESERVED_FIRST_CHARS});
            }
        } else if (this.angularPass) {
            if (reservedChars == null) {
                reservedChars = ANGULAR_PROPERTY_RESERVED_FIRST_CHARS;
            } else {
                reservedChars = Chars.concat(new char[][]{reservedChars, ANGULAR_PROPERTY_RESERVED_FIRST_CHARS});
            }
        }

        return reservedChars;
    }

    public char[] getPropertyReservedNamingNonFirstChars() {
        char[] reservedChars = null;
        if (this.polymerVersion != null && this.polymerVersion > 1) {
            if (reservedChars == null) {
                reservedChars = POLYMER_PROPERTY_RESERVED_NON_FIRST_CHARS;
            } else {
                reservedChars = Chars.concat(new char[][]{reservedChars, POLYMER_PROPERTY_RESERVED_NON_FIRST_CHARS});
            }
        }

        return reservedChars;
    }

    @GwtIncompatible("ObjectOutputStream")
    private void writeObject(ObjectOutputStream out) throws IOException {
        out.defaultWriteObject();
        out.writeObject(this.outputCharset == null ? null : this.outputCharset.name());
    }

    @GwtIncompatible("ObjectInputStream")
    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        in.defaultReadObject();
        String outputCharsetName = (String)in.readObject();
        if (outputCharsetName != null) {
            this.outputCharset = Charset.forName(outputCharsetName);
        }

    }

    boolean shouldOptimize() {
        return !this.skipNonTranspilationPasses && !this.checksOnly && !this.shouldGenerateTypedExterns() && !this.instrumentForCoverageOnly;
    }

    public static enum Reach {
        ALL,
        LOCAL_ONLY,
        NONE;

        public boolean isOn() {
            return this != NONE;
        }

        public boolean includesGlobals() {
            return this == ALL;
        }
    }

    public static enum PropertyCollapseLevel {
        ALL,
        NONE,
        MODULE_EXPORT;
    }

    private class BrowserFeaturesetYear implements Serializable {
        final int year;

        BrowserFeaturesetYear(int year) {
            Preconditions.checkState(year == 2012 || year >= 2018 && year <= 2024, "Illegal browser_featureset_year=%s. We support values 2012, or 2018..2024 only", year);
            this.year = year;
        }

        void setDependentValuesFromYear() {
            if (this.year == 2024) {
                CompilerOptions.this.setOutputFeatureSet(FeatureSet.BROWSER_2024);
            } else if (this.year == 2023) {
                CompilerOptions.this.setOutputFeatureSet(FeatureSet.BROWSER_2023);
            } else if (this.year == 2022) {
                CompilerOptions.this.setOutputFeatureSet(FeatureSet.BROWSER_2022);
            } else if (this.year == 2021) {
                CompilerOptions.this.setOutputFeatureSet(FeatureSet.BROWSER_2021);
            } else if (this.year == 2020) {
                CompilerOptions.this.setOutputFeatureSet(FeatureSet.BROWSER_2020);
            } else if (this.year == 2019) {
                CompilerOptions.this.setLanguageOut(CompilerOptions.LanguageMode.ECMASCRIPT_2017);
            } else if (this.year == 2018) {
                CompilerOptions.this.setLanguageOut(CompilerOptions.LanguageMode.ECMASCRIPT_2016);
            } else if (this.year == 2012) {
                CompilerOptions.this.setLanguageOut(CompilerOptions.LanguageMode.ECMASCRIPT5_STRICT);
            }

            CompilerOptions.this.setDefineToNumberLiteral("goog.FEATURESET_YEAR", this.year);
        }
    }

    public static enum IncrementalCheckMode {
        OFF,
        GENERATE_IJS,
        RUN_IJS_CHECKS_LATE;
    }

    public static enum ExtractPrototypeMemberDeclarationsMode {
        OFF,
        USE_GLOBAL_TEMP,
        USE_CHUNK_TEMP,
        USE_IIFE;
    }

    public static enum OutputJs {
        NONE,
        SENTINEL,
        NORMAL;
    }

    public static enum ExperimentalForceTranspile {
        LET_CONST,
        CLASS,
        ALL_EXCEPT_ASYNC_AWAIT;
    }

    public static enum Es6ModuleTranspilation {
        NONE,
        RELATIVIZE_IMPORT_PATHS,
        TO_COMMON_JS_LIKE_MODULES,
        COMPILE;
    }

    public static enum InstrumentOption {
        NONE,
        LINE_ONLY,
        BRANCH_ONLY,
        PRODUCTION;

        public static @Nullable InstrumentOption fromString(String value) {
            if (value == null) {
                return null;
            } else {
                switch (value) {
                    case "NONE":
                        return NONE;
                    case "LINE":
                        return LINE_ONLY;
                    case "BRANCH":
                        return BRANCH_ONLY;
                    case "PRODUCTION":
                        return PRODUCTION;
                    default:
                        return null;
                }
            }
        }
    }

    public static enum ChunkOutputType {
        GLOBAL_NAMESPACE,
        ES_MODULES;
    }

    public static enum LanguageMode {
        ECMASCRIPT3,
        ECMASCRIPT5,
        ECMASCRIPT5_STRICT,
        ECMASCRIPT_2015,
        ECMASCRIPT_2016,
        ECMASCRIPT_2017,
        ECMASCRIPT_2018,
        ECMASCRIPT_2019,
        ECMASCRIPT_2020,
        ECMASCRIPT_2021,
        ECMASCRIPT_NEXT,
        STABLE,
        NO_TRANSPILE,
        UNSTABLE,
        UNSUPPORTED;

        public static final LanguageMode STABLE_IN = ECMASCRIPT_NEXT;
        public static final LanguageMode STABLE_OUT = ECMASCRIPT5;

        boolean isDefaultStrict() {
            switch (this) {
                case ECMASCRIPT3:
                case ECMASCRIPT5:
                    return false;
                default:
                    return true;
            }
        }

        public static @Nullable LanguageMode fromString(String value) {
            if (value == null) {
                return null;
            } else {
                String canonicalizedName = Ascii.toUpperCase(value.trim()).replaceFirst("^ES", "ECMASCRIPT");
                if (!canonicalizedName.equals("ECMASCRIPT6") && !canonicalizedName.equals("ECMASCRIPT6_STRICT")) {
                    try {
                        return valueOf(canonicalizedName);
                    } catch (IllegalArgumentException var3) {
                        return null;
                    }
                } else {
                    return ECMASCRIPT_2015;
                }
            }
        }

        public FeatureSet toFeatureSet() {
            switch (this) {
                case ECMASCRIPT3:
                    return FeatureSet.ES3;
                case ECMASCRIPT5:
                case ECMASCRIPT5_STRICT:
                    return FeatureSet.ES5;
                case ECMASCRIPT_2015:
                    return FeatureSet.ES2015_MODULES;
                case ECMASCRIPT_2016:
                    return FeatureSet.ES2016_MODULES;
                case ECMASCRIPT_2017:
                    return FeatureSet.ES2017_MODULES;
                case ECMASCRIPT_2018:
                    return FeatureSet.ES2018_MODULES;
                case ECMASCRIPT_2019:
                    return FeatureSet.ES2019_MODULES;
                case ECMASCRIPT_2020:
                    return FeatureSet.ES2020_MODULES;
                case ECMASCRIPT_2021:
                    return FeatureSet.ES2021_MODULES;
                case ECMASCRIPT_NEXT:
                    return FeatureSet.ES_NEXT;
                case NO_TRANSPILE:
                case UNSTABLE:
                    return FeatureSet.ES_UNSTABLE;
                case UNSUPPORTED:
                    return FeatureSet.ES_UNSUPPORTED;
                case STABLE:
                    throw new UnsupportedOperationException("STABLE has different feature sets for language in and out. Use STABLE_IN or STABLE_OUT.");
                default:
                    throw new IllegalStateException();
            }
        }
    }

    public static enum DevMode {
        OFF,
        START,
        START_AND_END,
        EVERY_PASS;
    }

    public static enum TracerMode {
        ALL,
        RAW_SIZE,
        AST_SIZE,
        TIMING_ONLY,
        OFF;

        public boolean isOn() {
            return this != OFF;
        }
    }

    public static enum TweakProcessing {
        OFF,
        CHECK,
        STRIP;

        public boolean isOn() {
            return this != OFF;
        }

        public boolean shouldStrip() {
            return this == STRIP;
        }
    }

    public static enum IsolationMode {
        NONE,
        IIFE;
    }

    public static enum AliasStringsMode {
        NONE,
        LARGE,
        ALL;
    }

    private static class NullAliasTransformationHandler implements AliasTransformationHandler {
        private static final AliasTransformation NULL_ALIAS_TRANSFORMATION = new NullAliasTransformation();

        public AliasTransformation logAliasTransformation(String sourceFile, SourcePosition<AliasTransformation> position) {
            position.setItem(NULL_ALIAS_TRANSFORMATION);
            return NULL_ALIAS_TRANSFORMATION;
        }

        private static class NullAliasTransformation implements AliasTransformation {
            public void addAlias(String alias, String definition) {
            }
        }
    }

    public static enum Environment {
        BROWSER,
        CUSTOM;
    }

    static enum JsonStreamMode {
        NONE,
        IN,
        OUT,
        BOTH;
    }

    public static enum J2clPassMode {
        OFF,
        AUTO;

        boolean shouldAddJ2clPasses() {
            return this == AUTO;
        }
    }

    public interface AliasTransformation {
        void addAlias(String alias, String definition);
    }

    public interface AliasTransformationHandler {
        AliasTransformation logAliasTransformation(String sourceFile, SourcePosition<AliasTransformation> position);
    }
}
