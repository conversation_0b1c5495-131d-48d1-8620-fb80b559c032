Sencha Cmd 7.9.0.12345 - Crash report
================================================
An error occurred while executing the following command: app build

Diagnostic information:
=======================
                         app.archivePath : archive                                           
                      app.bootstrap.base : /Users/<USER>/Downloads/MyAppName               
                       app.bootstrap.css : bootstrap.css                                     
               app.bootstrap.microloader : bootstrap.js                                      
                        app.cache.deltas : true                                              
                        app.cache.enable : false                                             
                           app.classpath : app                                               
                         app.classpath.0 : app                                               
                    app.classpath.length : 1                                                 
                          app.compressor : null                                              
                          app.config.dir : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app
                app.cordova.cache.enable : false                                             
                   app.cordova.config.id : com.domain.MyAppName                              
                 app.cordova.config.name : MyAppName                                         
                 app.cordova.config.path : /Users/<USER>/Downloads/MyAppName/cordova       
               app.cordova.config.target :                                                   
              app.cordova.config.verbose : false                                             
                   app.cordova.js.0.path : cordova.js                                        
               app.cordova.js.0.priority : 1000                                              
                 app.cordova.js.0.remote : true                                              
                   app.cordova.js.length : 1                                                 
                 app.cordova.microloader : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/Microloader.js
      app.cordova.output.appCache.enable : false                                             
                 app.cordova.output.base : /Users/<USER>/Downloads/MyAppName/cordova/www   
             app.cordova.output.manifest : ${build.id}.json                                  
                 app.cordova.output.page : index.html                                        
             app.development.watch.delay : 250                                               
                                 app.dir : /Users/<USER>/Downloads/MyAppName               
              app.fashion.inliner.enable : false                                             
           app.fashion.missingParameters : error                                             
                           app.framework : ext                                               
                                  app.id : 071350e7-d517-42fd-80a9-50fd54de6e8a              
                       app.indexHtmlPath : index.html                                        
                        app.loader.cache : false                                             
                   app.loader.cacheParam : _dc                                               
                                app.name : MyAppName                                         
                           app.namespace : MyAppName                                         
              app.output.appCache.enable : false                                             
                         app.output.base : /Users/<USER>/Downloads/MyAppName/build/${build.environment}/MyAppName
           app.output.microloader.enable : true                                              
                           app.overrides : overrides                                         
                         app.overrides.0 : overrides                                         
                    app.overrides.length : 1                                                 
               app.phonegap.cache.enable : false                                             
                  app.phonegap.config.id : com.domain.MyAppName                              
                app.phonegap.config.name : MyAppName                                         
                app.phonegap.config.path : /Users/<USER>/Downloads/MyAppName/phonegap      
              app.phonegap.config.remote : false                                             
             app.phonegap.config.verbose : false                                             
                  app.phonegap.js.0.path : cordova.js                                        
              app.phonegap.js.0.priority : 1000                                              
                app.phonegap.js.0.remote : true                                              
                  app.phonegap.js.length : 1                                                 
                app.phonegap.microloader : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/Microloader.js
     app.phonegap.output.appCache.enable : false                                             
                app.phonegap.output.base : /Users/<USER>/Downloads/MyAppName/phonegap/www  
            app.phonegap.output.manifest : ${build.id}.json                                  
                app.phonegap.output.page : index.html                                        
             app.production.cache.enable : true                                              
          app.production.compressor.type : closure                                           
       app.production.language.js.output : ES5                                               
             app.production.loader.cache : ${build.timestamp}                                
   app.production.output.appCache.enable : false                                             
     app.production.output.appCache.path : cache.appcache                                    
                        app.sass.dynamic : true                                              
                            app.sass.etc : sass/etc/all.scss                                 
                          app.sass.etc.0 : sass/etc/all.scss                                 
                     app.sass.etc.length : 1                                                 
                        app.sass.fashion : true                                              
                  app.sass.generated.src : sass/save                                         
                  app.sass.generated.var : sass/save.scss                                    
                      app.sass.namespace : MyAppName                                         
                            app.sass.src : sass/src                                          
                          app.sass.src.0 : sass/src                                          
                     app.sass.src.length : 1                                                 
                            app.sass.var : sass/var/all.scss,sass/var                        
                          app.sass.var.0 : sass/var/all.scss                                 
                          app.sass.var.1 : sass/var                                          
                     app.sass.var.length : 2                                                 
                 app.slicer.cache.enable : false                                             
        app.slicer.js.0.isWidgetManifest : true                                              
                    app.slicer.js.0.path : sass/example/custom.js                            
                    app.slicer.js.length : 1                                                 
       app.slicer.output.appCache.enable : false                                             
                               app.theme : theme-triton                                      
                             app.toolkit : classic                                           
                             app.version : 1.0.0.0                                           
                    buildenvironment.dir : /Users/<USER>/Downloads/MyAppName               
               buildenvironment.load.dir : /Users/<USER>/Downloads/MyAppName               
                          cmd.config.dir : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd
                                 cmd.dir : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd
                    cmd.framework.minver : 5.5.0                                             
                   cmd.framework.version : 99.99                                             
              cmd.merge.tool.args.araxis : -wait -merge -3 -a1 {base} {user} {generated} {out}
              cmd.merge.tool.args.kdiff3 : {base} {user} {generated} -o {out}                
             cmd.merge.tool.args.p4merge : {base} {user} {generated} {out}                   
           cmd.merge.tool.args.smartsync : {user} {generated} {base}                         
          cmd.merge.tool.args.sourcegear : --merge --result={out} {user} {base} {generated}  
            cmd.merge.tool.args.tortoise : -base:{base} -theirs:{generated} -mine:{user} -merged:{out}
                              cmd.minver : *******                                           
                            cmd.platform : osx                                               
                     cmd.unicode.escapes : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/unicode-escapes.json
                             cmd.version : 7.9.0.12345                                       
                            cmd.web.port : 1841                                              
                     config.skipDownload : true                                              
                      config.skipInherit : false                                             
                        ext.license.name : commercial                                        
                     framework.build.dir : ${package.output.base}                            
                     framework.classpath : ${package.dir}/src                                
                    framework.cmd.minver : ********                                          
                   framework.cmd.version : ********                                          
                 framework.compatVersion : 7.4.0                                             
                    framework.config.dir : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/plugins/ext/current
                       framework.creator : Sencha                                            
           framework.detailedDescription : Sencha Ext JS JavaScript Framework                
                           framework.dir : /Users/<USER>/Downloads/MyAppName/ext           
                  framework.example.apps : admin-dashboard,executive-dashboard,feed-viewer,kitchensink,calendar,classic/desktop,classic/ticket-app,classic/portal,classic/simple-tasks,classic/responsive-app,classic/aria,modern/addressbook,modern/blackberry,modern/energy,modern/stockapp,modern/states,modern/geocongress,modern/oreilly
                framework.example.apps.0 : admin-dashboard                                   
                framework.example.apps.1 : executive-dashboard                               
               framework.example.apps.10 : classic/aria                                      
               framework.example.apps.11 : modern/addressbook                                
               framework.example.apps.12 : modern/blackberry                                 
               framework.example.apps.13 : modern/energy                                     
               framework.example.apps.14 : modern/stockapp                                   
               framework.example.apps.15 : modern/states                                     
               framework.example.apps.16 : modern/geocongress                                
               framework.example.apps.17 : modern/oreilly                                    
                framework.example.apps.2 : feed-viewer                                       
                framework.example.apps.3 : kitchensink                                       
                framework.example.apps.4 : calendar                                          
                framework.example.apps.5 : classic/desktop                                   
                framework.example.apps.6 : classic/ticket-app                                
                framework.example.apps.7 : classic/portal                                    
                framework.example.apps.8 : classic/simple-tasks                              
                framework.example.apps.9 : classic/responsive-app                            
           framework.example.apps.length : 18                                                
                  framework.example.path : examples,templates                                
                framework.example.path.0 : examples                                          
                framework.example.path.1 : templates                                         
           framework.example.path.length : 2                                                 
                        framework.format : 1                                                 
                          framework.isV5 : true                                              
                          framework.isV6 : true                                              
                         framework.isV62 : true                                              
                         framework.isV65 : true                                              
     framework.language.js.input.version : ES5                                               
                          framework.name : ext                                               
                     framework.namespace : Ext                                               
                        framework.output : ${package.dir}/build                              
                     framework.overrides : ${package.dir}/overrides                          
                  framework.packages.dir : /Users/<USER>/Downloads/MyAppName/ext/packages/,/Users/<USER>/Downloads/MyAppName/ext/classic
                framework.resource.paths : resources                                         
              framework.resource.paths.0 : resources                                         
         framework.resource.paths.length : 1                                                 
                      framework.sass.etc : ${package.dir}/sass/etc/all.scss                  
                  framework.sass.fashion : true                                              
                framework.sass.namespace : Ext                                               
                      framework.sass.src : ${package.dir}/sass/src                           
                      framework.sass.var : ${package.dir}/sass/var                           
        framework.signatures.0.algorithm : SHA1withRSA                                       
          framework.signatures.0.created : 2021-05-04T14:49:33Z                              
             framework.signatures.0.name : Sencha                                            
            framework.signatures.0.nonce : khtoW8rALTY=                                      
        framework.signatures.0.signature : H6VStiqWlUVnhr37GM5/MdRFgHyukkJISZsAoG1XjKdJTWKerJK7dMA7YAHpb3rpUJ8WQYxRKPUPrwsE1DYPVLeLzkSC1pRlngru9OYsuTvQeyW8y6/KCbdMVgbFJA6MJK/cMvzRBKe7v1Xv6IAx3cjj9MnU9mY5IN5lIgUOJMHtSGNIJayXqhgrtRdcVhR/f4LNeyVbreCF0/FVoOGtbpr/7B9TP1Bg1bHVxqoq/nWgS8h4GVGRAX6bAS94XhLwhD1+jYDlKE2N9WhYKbwI0vcc3V/eWKv+mhtC6SMY+p5lRmZ/HQPTMx56oVollhTp4d9BjRqgpWX7EalmB0SYjw==
             framework.signatures.0.uuid : 1d6b0d9c-3333-4e65-885f-5b07a1fc3198              
             framework.signatures.length : 1                                                 
                   framework.subpkgs.dir : ${package.dir}                                    
              framework.subpkgs.packages : classic/classic,modern/modern,packages/charts,packages/amf,packages/soap,packages/ux,packages/google,packages/legacy,packages/font-awesome,packages/font-pictos,packages/font-ext
            framework.subpkgs.packages.0 : classic/classic                                   
            framework.subpkgs.packages.1 : modern/modern                                     
           framework.subpkgs.packages.10 : packages/font-ext                                 
            framework.subpkgs.packages.2 : packages/charts                                   
            framework.subpkgs.packages.3 : packages/amf                                      
            framework.subpkgs.packages.4 : packages/soap                                     
            framework.subpkgs.packages.5 : packages/ux                                       
            framework.subpkgs.packages.6 : packages/google                                   
            framework.subpkgs.packages.7 : packages/legacy                                   
            framework.subpkgs.packages.8 : packages/font-awesome                             
            framework.subpkgs.packages.9 : packages/font-pictos                              
       framework.subpkgs.packages.length : 11                                                
                       framework.summary : Ext JS                                            
                          framework.type : framework                                         
                       framework.version : ********                                          
                        git.current.hash : 669f575eb1592a96aa3fb58a602faf3b96d819ea          
                       inspector.address : http://localhost:1839/                            
                            minimal.mode : true                                              
                    package.sass.dynamic : true                                              
                          repo.local.dir : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/repo
                       shared.sencha.dir : true                                              
        system.java.net.useSystemProxies : true                                              
                              theme.name : theme-triton                                      
                            toolkit.name : classic                                           
                          workspace.apps :                                                   
                        workspace.apps.0 :                                                   
                   workspace.apps.length : 1                                                 
                     workspace.build.dir : /Users/<USER>/Downloads/MyAppName/build         
                    workspace.config.dir : /Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/workspace
                           workspace.dir : /Users/<USER>/Downloads/MyAppName               
           workspace.frameworks.ext.path : /Users/<USER>/Downloads/MyAppName/ext           
        workspace.frameworks.ext.version : ********                                          
                  workspace.packages.dir : /Users/<USER>/Downloads/MyAppName/packages/local,/Users/<USER>/Downloads/MyAppName/packages
              workspace.packages.extract : /Users/<USER>/Downloads/MyAppName/packages/remote

Exception information:
=======================
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/plugin.xml:333: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/build-impl.xml:341: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:378: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:379: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:107: com.sencha.exceptions.ExArg: Unknown command: "fashion"
     at com.sencha.ant.AntScript.execute(AntScript.java:121)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)
   Caused by: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/build-impl.xml:341: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:378: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:379: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:107: com.sencha.exceptions.ExArg: Unknown command: "fashion"
     at org.apache.tools.ant.ProjectHelper.addLocationToBuildException(ProjectHelper.java:551)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:444)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.Project.executeTarget(Project.java:1368)
     at org.apache.tools.ant.helper.DefaultExecutor.executeTargets(DefaultExecutor.java:41)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at com.sencha.ant.AntScript.execute(AntScript.java:117)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)
   Caused by: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:378: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:379: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:107: com.sencha.exceptions.ExArg: Unknown command: "fashion"
     at org.apache.tools.ant.ProjectHelper.addLocationToBuildException(ProjectHelper.java:551)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:444)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.Project.executeTarget(Project.java:1368)
     at org.apache.tools.ant.helper.DefaultExecutor.executeTargets(DefaultExecutor.java:41)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at com.sencha.ant.AntScript.execute(AntScript.java:117)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)
   Caused by: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:379: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:107: com.sencha.exceptions.ExArg: Unknown command: "fashion"
     at org.apache.tools.ant.ProjectHelper.addLocationToBuildException(ProjectHelper.java:551)
     at org.apache.tools.ant.taskdefs.MacroInstance.execute(MacroInstance.java:401)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.Project.executeTarget(Project.java:1368)
     at org.apache.tools.ant.helper.DefaultExecutor.executeTargets(DefaultExecutor.java:41)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at com.sencha.ant.AntScript.execute(AntScript.java:117)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)
   Caused by: The following error occurred while executing this line:
/Users/<USER>/Documents/Projects/cmd/sencha-command/dist/SenchaCmd/ant/build/app/slice-impl.xml:107: com.sencha.exceptions.ExArg: Unknown command: "fashion"
     at org.apache.tools.ant.ProjectHelper.addLocationToBuildException(ProjectHelper.java:551)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:444)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:197)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.MacroInstance.execute(MacroInstance.java:398)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.Project.executeTarget(Project.java:1368)
     at org.apache.tools.ant.helper.DefaultExecutor.executeTargets(DefaultExecutor.java:41)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at com.sencha.ant.AntScript.execute(AntScript.java:117)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)
   Caused by: com.sencha.exceptions.ExArg: Unknown command: "fashion"
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:116)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:197)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:197)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.MacroInstance.execute(MacroInstance.java:398)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.Project.executeTarget(Project.java:1368)
     at org.apache.tools.ant.helper.DefaultExecutor.executeTargets(DefaultExecutor.java:41)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at com.sencha.ant.AntScript.execute(AntScript.java:117)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)
   Caused by: Unknown command: "fashion"
     at com.sencha.cli.Commands.findCommand(Commands.java:81)
     at com.sencha.cli.Commands.dispatch(Commands.java:61)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.cli.AbstractCommand.dispatch(AbstractCommand.java:124)
     at com.sencha.ant.SenchaCommandTask.doExecute(SenchaCommandTask.java:42)
     at com.sencha.ant.BaseAntTask.execute(BaseAntTask.java:34)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:197)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:197)
     at jdk.internal.reflect.GeneratedMethodAccessor47.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.MacroInstance.execute(MacroInstance.java:398)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.taskdefs.CallTarget.execute(CallTarget.java:105)
     at com.sencha.ant.CallTask.execute(CallTask.java:137)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.helper.SingleCheckExecutor.executeTargets(SingleCheckExecutor.java:38)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at org.apache.tools.ant.taskdefs.Ant.execute(Ant.java:442)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at jdk.internal.reflect.GeneratedMethodAccessor33.invoke(Unknown Source)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.taskdefs.Sequential.execute(Sequential.java:68)
     at net.sf.antcontrib.logic.IfTask.execute(IfTask.java:217)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.TaskAdapter.execute(TaskAdapter.java:154)
     at org.apache.tools.ant.UnknownElement.execute(UnknownElement.java:291)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at org.apache.tools.ant.dispatch.DispatchUtils.execute(DispatchUtils.java:106)
     at org.apache.tools.ant.Task.perform(Task.java:348)
     at org.apache.tools.ant.Target.execute(Target.java:392)
     at org.apache.tools.ant.Target.performTasks(Target.java:413)
     at org.apache.tools.ant.Project.executeSortedTargets(Project.java:1399)
     at org.apache.tools.ant.Project.executeTarget(Project.java:1368)
     at org.apache.tools.ant.helper.DefaultExecutor.executeTargets(DefaultExecutor.java:41)
     at org.apache.tools.ant.Project.executeTargets(Project.java:1251)
     at com.sencha.ant.AntScript.execute(AntScript.java:117)
     at com.sencha.command.plugin.PluginManager.execute(PluginManager.java:104)
     at com.sencha.command.plugin.PluginManager.executeReverseFirst(PluginManager.java:145)
     at com.sencha.command.environment.BuildEnvironment.execute(BuildEnvironment.java:309)
     at com.sencha.command.environment.AppOrPackageEnvironment.execute(AppOrPackageEnvironment.java:729)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.doExecute(BasePluginCommands.java:125)
     at com.sencha.command.app.AppCommands$BuildCommand.execute(AppCommands.java:470)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
     at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
     at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
     at java.base/java.lang.reflect.Method.invoke(Method.java:569)
     at com.sencha.util.MethodInvoker$Arguments.invoke(MethodInvoker.java:175)
     at com.sencha.cli.Command.dispatch(Command.java:43)
     at com.sencha.command.BasePluginCommands$BasePluginCommand.dispatch(BasePluginCommands.java:289)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.cli.Commands.dispatch(Commands.java:64)
     at com.sencha.command.Sencha.dispatch(Sencha.java:80)
     at com.sencha.command.Sencha.main(Sencha.java:153)

